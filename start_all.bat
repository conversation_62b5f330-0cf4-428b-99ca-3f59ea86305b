@echo off
cls
echo ================================================================================
echo                     QUANTUM INVESTMENT PLATFORM
echo                        Professional Trading System
echo ================================================================================
echo.

:: Set console title
title Quantum Investment Platform - Launcher

:: Check requirements
echo [1/5] Checking System Requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)
echo       [OK] Python installed

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    pause
    exit /b 1
)
echo       [OK] Node.js installed
echo.

:: Start Backend
echo [2/5] Starting Backend Server...
cd backend
if not exist app\main_fixed.py (
    echo [ERROR] Backend files not found
    pause
    exit /b 1
)

:: Install backend dependencies if needed
pip show fastapi >nul 2>&1
if errorlevel 1 (
    echo       Installing backend dependencies...
    pip install fastapi uvicorn sqlalchemy aiosqlite pydantic pydantic-settings python-dotenv -q
)

:: Start backend in new window
start "Quantum Platform - Backend" cmd /k python app\main_fixed.py
echo       [OK] Backend starting on http://localhost:8000
timeout /t 3 /nobreak >nul
echo.

:: Start Frontend
echo [3/5] Starting Frontend Server...
cd ..\frontend

:: Check if node_modules exists
if not exist node_modules (
    echo       Installing frontend dependencies (this may take a few minutes)...
    call npm install --silent
)

:: Ensure main.ts uses full application
if exist src\main_full.ts (
    copy /Y src\main_full.ts src\main.ts >nul 2>&1
)

:: Start frontend in new window
start "Quantum Platform - Frontend" cmd /k npm run dev
echo       [OK] Frontend starting on http://localhost:5173
timeout /t 5 /nobreak >nul
echo.

:: Wait for services
echo [4/5] Waiting for services to initialize...
:wait_backend
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto wait_backend
)
echo       [OK] Backend is ready

:wait_frontend
curl -s http://localhost:5173 >nul 2>&1
if errorlevel 1 (
    timeout /t 1 /nobreak >nul
    goto wait_frontend
)
echo       [OK] Frontend is ready
echo.

:: Display status
echo [5/5] Platform Started Successfully!
echo.
echo ================================================================================
echo                           ACCESS INFORMATION
echo ================================================================================
echo.
echo   Frontend Application:  http://localhost:5173
echo   Backend API:          http://localhost:8000
echo   API Documentation:    http://localhost:8000/docs
echo   API Interactive:      http://localhost:8000/redoc
echo.
echo ================================================================================
echo                           SERVICE STATUS
echo ================================================================================
echo.
echo   [RUNNING] Backend Server   - Port 8000
echo   [RUNNING] Frontend Server  - Port 5173
echo   [RUNNING] Database        - SQLite (data/quantplatform.db)
echo   [RUNNING] WebSocket       - ws://localhost:8000/api/v1/ws
echo.
echo ================================================================================
echo.
echo Press any key to open the platform in your browser...
pause >nul

:: Open in browser
start http://localhost:5173

echo.
echo Platform is running. Press Ctrl+C in service windows to stop.
echo This window can be closed safely.
echo.
pause