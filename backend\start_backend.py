#!/usr/bin/env python3
"""
量化投资平台后端启动脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ.setdefault("ENV", "development")
os.environ.setdefault("DEBUG", "true")

if __name__ == "__main__":
    try:
        import uvicorn

        # 优先使用主入口点，按优先级尝试
        app_module = None
        try:
            from app.main import app
            app_module = "app.main:app"
            print("使用完整版后端服务 (app.main)...")
        except ImportError:
            try:
                from app.main_fixed import app
                app_module = "app.main_fixed:app"
                print("使用修复版后端服务 (app.main_fixed)...")
            except ImportError:
                try:
                    from app.main_simple import app
                    app_module = "app.main_simple:app"
                    print("使用简化版后端服务 (app.main_simple)...")
                except ImportError:
                    print("错误: 找不到可用的主应用文件")
                    print("请确保存在以下文件之一:")
                    print("- app/main.py")
                    print("- app/main_fixed.py")
                    print("- app/main_simple.py")
                    sys.exit(1)

        print("启动量化投资平台后端服务...")
        print("地址: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        print("健康检查: http://localhost:8000/health")

        uvicorn.run(
            app_module,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查依赖是否安装完整")
        sys.exit(1)