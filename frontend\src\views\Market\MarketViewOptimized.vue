<template>
  <div class="market-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>市场行情</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="connectWebSocket">
          <el-icon><Connection /></el-icon>
          {{ wsConnected ? '已连接' : '连接实时数据' }}
        </el-button>
      </div>
    </div>

    <!-- 市场概览卡片 -->
    <el-row :gutter="20" class="market-overview">
      <el-col :xs="24" :sm="12" :md="6" v-for="index in marketIndices" :key="index.code">
        <el-card class="index-card">
          <div class="index-name">{{ index.name }}</div>
          <div class="index-value">{{ index.value }}</div>
          <div class="index-change" :class="index.change > 0 ? 'up' : 'down'">
            <span>{{ index.change > 0 ? '+' : '' }}{{ index.change }}%</span>
            <el-icon v-if="index.change > 0"><Top /></el-icon>
            <el-icon v-else><Bottom /></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索和筛选 -->
    <div class="search-filter">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索股票代码或名称"
        :prefix-icon="Search"
        clearable
        style="width: 300px"
        @input="handleSearch"
      />
      <el-select v-model="selectedMarket" placeholder="选择市场" style="width: 150px">
        <el-option label="全部市场" value="" />
        <el-option label="沪市" value="SH" />
        <el-option label="深市" value="SZ" />
        <el-option label="创业板" value="CYB" />
        <el-option label="科创板" value="KCB" />
      </el-select>
      <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px">
        <el-option label="默认排序" value="" />
        <el-option label="涨幅从高到低" value="change_desc" />
        <el-option label="涨幅从低到高" value="change_asc" />
        <el-option label="成交量" value="volume" />
        <el-option label="成交额" value="amount" />
      </el-select>
    </div>

    <!-- 股票列表表格 -->
    <el-table
      :data="filteredStocks"
      v-loading="loading"
      style="width: 100%"
      height="600"
      @row-click="handleStockClick"
      class="stock-table"
    >
      <el-table-column prop="symbol" label="代码" width="100" fixed="left" />
      <el-table-column prop="name" label="名称" width="120" fixed="left" />
      <el-table-column prop="price" label="现价" width="100">
        <template #default="{ row }">
          <span :class="row.change > 0 ? 'price-up' : 'price-down'">
            {{ row.price.toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="change" label="涨跌幅" width="120">
        <template #default="{ row }">
          <div :class="row.change > 0 ? 'change-up' : 'change-down'">
            {{ row.change > 0 ? '+' : '' }}{{ row.change.toFixed(2) }}%
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="volume" label="成交量" width="120">
        <template #default="{ row }">
          {{ formatVolume(row.volume) }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="成交额" width="120">
        <template #default="{ row }">
          {{ formatAmount(row.amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="high" label="最高" width="100" />
      <el-table-column prop="low" label="最低" width="100" />
      <el-table-column prop="open" label="开盘" width="100" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click.stop="viewChart(row)">K线</el-button>
          <el-button size="small" type="primary" @click.stop="addToWatchlist(row)">关注</el-button>
          <el-button size="small" type="success" @click.stop="quickTrade(row)">交易</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- K线图对话框 -->
    <el-dialog
      v-model="chartDialogVisible"
      :title="`${selectedStock?.name} (${selectedStock?.symbol}) - K线图`"
      width="80%"
      destroy-on-close
    >
      <div ref="chartContainer" style="width: 100%; height: 500px;"></div>
    </el-dialog>

    <!-- 快速交易对话框 -->
    <el-dialog
      v-model="tradeDialogVisible"
      title="快速交易"
      width="500px"
    >
      <el-form :model="tradeForm" label-width="100px">
        <el-form-item label="股票">
          <el-input :value="`${tradeForm.symbol} - ${tradeForm.name}`" disabled />
        </el-form-item>
        <el-form-item label="现价">
          <el-input :value="tradeForm.price" disabled />
        </el-form-item>
        <el-form-item label="买卖方向">
          <el-radio-group v-model="tradeForm.side">
            <el-radio-button label="buy">买入</el-radio-button>
            <el-radio-button label="sell">卖出</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数量">
          <el-input-number
            v-model="tradeForm.quantity"
            :min="100"
            :step="100"
            placeholder="请输入数量"
          />
        </el-form-item>
        <el-form-item label="订单类型">
          <el-radio-group v-model="tradeForm.orderType">
            <el-radio label="limit">限价单</el-radio>
            <el-radio label="market">市价单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="价格" v-if="tradeForm.orderType === 'limit'">
          <el-input-number
            v-model="tradeForm.orderPrice"
            :precision="2"
            :step="0.01"
            placeholder="请输入价格"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="tradeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitTrade" :loading="tradeLoading">
          确认下单
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Refresh, Connection, Search, Top, Bottom } from '@element-plus/icons-vue'
import { optimizedAPI } from '@/api/optimized-api'
import * as echarts from 'echarts'

// 状态
const loading = ref(false)
const wsConnected = ref(false)
const chartDialogVisible = ref(false)
const tradeDialogVisible = ref(false)
const tradeLoading = ref(false)

// 数据
const stocks = ref<any[]>([])
const selectedStock = ref<any>(null)
const searchKeyword = ref('')
const selectedMarket = ref('')
const sortBy = ref('')
const chartContainer = ref()

// 市场指数
const marketIndices = ref([
  { code: '000001', name: '上证指数', value: '3156.23', change: 0.58 },
  { code: '399001', name: '深证成指', value: '10523.45', change: -0.23 },
  { code: '399006', name: '创业板指', value: '2234.56', change: 1.25 },
  { code: '000688', name: '科创50', value: '1089.34', change: -0.12 },
])

// 交易表单
const tradeForm = reactive({
  symbol: '',
  name: '',
  price: 0,
  side: 'buy',
  quantity: 100,
  orderType: 'limit',
  orderPrice: 0,
})

// 计算属性：过滤和排序后的股票列表
const filteredStocks = computed(() => {
  let result = [...stocks.value]

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(stock =>
      stock.symbol.toLowerCase().includes(keyword) ||
      stock.name.toLowerCase().includes(keyword)
    )
  }

  // 市场过滤
  if (selectedMarket.value) {
    result = result.filter(stock =>
      stock.symbol.endsWith(`.${selectedMarket.value}`)
    )
  }

  // 排序
  if (sortBy.value) {
    switch (sortBy.value) {
      case 'change_desc':
        result.sort((a, b) => b.change - a.change)
        break
      case 'change_asc':
        result.sort((a, b) => a.change - b.change)
        break
      case 'volume':
        result.sort((a, b) => b.volume - a.volume)
        break
      case 'amount':
        result.sort((a, b) => b.amount - a.amount)
        break
    }
  }

  return result
})

// 获取股票列表
const fetchStockList = async () => {
  loading.value = true
  try {
    const response = await optimizedAPI.market.getStockList()
    
    // 添加模拟的额外数据
    stocks.value = response.data.map((stock: any) => ({
      ...stock,
      volume: Math.floor(Math.random() * 10000000),
      amount: Math.floor(Math.random() * 1000000000),
      high: stock.price * 1.02,
      low: stock.price * 0.98,
      open: stock.price * (1 + Math.random() * 0.02 - 0.01),
    }))
  } catch (error) {
    console.error('获取股票列表失败:', error)
    ElMessage.error('获取股票列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchStockList()
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

// 查看K线图
const viewChart = async (stock: any) => {
  selectedStock.value = stock
  chartDialogVisible.value = true

  // 获取K线数据
  try {
    const response = await optimizedAPI.market.getKlineData(stock.symbol)
    
    // 等待对话框渲染完成
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 初始化图表
    if (chartContainer.value) {
      const chart = echarts.init(chartContainer.value)
      
      // 准备数据
      const dates = response.data.map((item: any) => item.date)
      const data = response.data.map((item: any) => [
        item.open,
        item.close,
        item.low,
        item.high
      ])
      const volumes = response.data.map((item: any) => item.volume)

      const option = {
        title: {
          text: `${stock.name} (${stock.symbol})`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['K线', '成交量'],
          top: 30
        },
        grid: [
          {
            left: '10%',
            right: '10%',
            top: '15%',
            height: '50%'
          },
          {
            left: '10%',
            right: '10%',
            top: '70%',
            height: '15%'
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: dates,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: 'dataMin',
            max: 'dataMax'
          },
          {
            type: 'category',
            gridIndex: 1,
            data: dates,
            boundaryGap: false,
            axisLine: { onZero: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: { show: false },
            min: 'dataMin',
            max: 'dataMax'
          }
        ],
        yAxis: [
          {
            scale: true,
            splitArea: {
              show: true
            }
          },
          {
            scale: true,
            gridIndex: 1,
            splitNumber: 2,
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1],
            start: 50,
            end: 100
          },
          {
            show: true,
            xAxisIndex: [0, 1],
            type: 'slider',
            top: '90%',
            start: 50,
            end: 100
          }
        ],
        series: [
          {
            name: 'K线',
            type: 'candlestick',
            data: data,
            itemStyle: {
              color: '#ef4444',
              color0: '#22c55e',
              borderColor: '#ef4444',
              borderColor0: '#22c55e'
            }
          },
          {
            name: '成交量',
            type: 'bar',
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: volumes,
            itemStyle: {
              color: '#3b82f6'
            }
          }
        ]
      }

      chart.setOption(option)

      // 自适应窗口大小
      window.addEventListener('resize', () => {
        chart.resize()
      })
    }
  } catch (error) {
    console.error('获取K线数据失败:', error)
    ElMessage.error('获取K线数据失败')
  }
}

// 添加到自选
const addToWatchlist = (stock: any) => {
  ElMessage.success(`已将 ${stock.name} 添加到自选`)
}

// 快速交易
const quickTrade = (stock: any) => {
  tradeForm.symbol = stock.symbol
  tradeForm.name = stock.name
  tradeForm.price = stock.price
  tradeForm.orderPrice = stock.price
  tradeDialogVisible.value = true
}

// 提交交易
const submitTrade = async () => {
  tradeLoading.value = true
  try {
    const order = {
      symbol: tradeForm.symbol,
      side: tradeForm.side as 'buy' | 'sell',
      quantity: tradeForm.quantity,
      price: tradeForm.orderType === 'limit' ? tradeForm.orderPrice : undefined,
      order_type: tradeForm.orderType as 'limit' | 'market'
    }

    const response = await optimizedAPI.trading.createOrder(order)
    
    ElNotification({
      title: '下单成功',
      message: `${tradeForm.side === 'buy' ? '买入' : '卖出'} ${tradeForm.name} ${tradeForm.quantity}股`,
      type: 'success'
    })

    tradeDialogVisible.value = false
  } catch (error) {
    console.error('下单失败:', error)
    ElMessage.error('下单失败')
  } finally {
    tradeLoading.value = false
  }
}

// 处理股票点击
const handleStockClick = (row: any) => {
  viewChart(row)
}

// WebSocket连接
const connectWebSocket = () => {
  if (wsConnected.value) {
    optimizedAPI.ws.disconnect()
    wsConnected.value = false
    ElMessage.info('已断开实时数据连接')
  } else {
    optimizedAPI.ws.connect()
    
    optimizedAPI.ws.on('connected', () => {
      wsConnected.value = true
      ElMessage.success('实时数据连接成功')
    })

    optimizedAPI.ws.on('market_data', (data: any) => {
      // 更新实时数据
      const stockIndex = stocks.value.findIndex(s => s.symbol === data.data?.symbol)
      if (stockIndex !== -1) {
        stocks.value[stockIndex] = {
          ...stocks.value[stockIndex],
          ...data.data
        }
      }
    })

    optimizedAPI.ws.on('disconnected', () => {
      wsConnected.value = false
    })
  }
}

// 格式化成交量
const formatVolume = (volume: number) => {
  if (volume > 100000000) {
    return (volume / 100000000).toFixed(2) + '亿'
  } else if (volume > 10000) {
    return (volume / 10000).toFixed(2) + '万'
  }
  return volume.toString()
}

// 格式化成交额
const formatAmount = (amount: number) => {
  if (amount > 100000000) {
    return (amount / 100000000).toFixed(2) + '亿'
  } else if (amount > 10000) {
    return (amount / 10000).toFixed(2) + '万'
  }
  return amount.toString()
}

// 生命周期
onMounted(() => {
  fetchStockList()
})

onUnmounted(() => {
  if (wsConnected.value) {
    optimizedAPI.ws.disconnect()
  }
})
</script>

<style scoped lang="scss">
.market-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.market-overview {
  margin-bottom: 20px;

  .index-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .index-name {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .index-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .index-change {
      display: flex;
      align-items: center;
      font-size: 14px;

      &.up {
        color: #ef4444;
      }

      &.down {
        color: #22c55e;
      }

      .el-icon {
        margin-left: 4px;
      }
    }
  }
}

.search-filter {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.stock-table {
  .price-up {
    color: #ef4444;
    font-weight: bold;
  }

  .price-down {
    color: #22c55e;
    font-weight: bold;
  }

  .change-up {
    color: #ef4444;
    font-weight: bold;
  }

  .change-down {
    color: #22c55e;
    font-weight: bold;
  }

  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>