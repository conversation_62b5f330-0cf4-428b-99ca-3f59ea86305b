/**
 * 优化的API客户端 - 与优化后的后端对接
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// ==================== 认证API ====================

export const authAPI = {
  // 登录
  login: async (username: string, password: string) => {
    const response = await apiClient.post('/api/v1/auth/login', {
      username,
      password,
    })
    
    // 保存token
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token)
      localStorage.setItem('user_info', JSON.stringify(response.data.user_info))
    }
    
    return response.data
  },

  // 注册
  register: async (username: string, password: string, email?: string) => {
    const response = await apiClient.post('/api/v1/auth/register', {
      username,
      password,
      email,
    })
    return response.data
  },

  // 登出
  logout: async () => {
    const response = await apiClient.post('/api/v1/auth/logout')
    localStorage.removeItem('access_token')
    localStorage.removeItem('user_info')
    return response.data
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    const userInfo = localStorage.getItem('user_info')
    return userInfo ? JSON.parse(userInfo) : null
  },

  // 检查是否已登录
  isAuthenticated: () => {
    return !!localStorage.getItem('access_token')
  },
}

// ==================== 市场数据API ====================

export const marketAPI = {
  // 获取股票列表
  getStockList: async () => {
    const response = await apiClient.get('/api/v1/market/stocks')
    return response.data
  },

  // 获取实时行情
  getRealtimeData: async (symbol: string) => {
    const response = await apiClient.get(`/api/v1/market/realtime/${symbol}`)
    return response.data
  },

  // 获取K线数据
  getKlineData: async (symbol: string, period: string = '1d', limit: number = 100) => {
    const response = await apiClient.get(`/api/v1/market/kline/${symbol}`, {
      params: { period, limit },
    })
    return response.data
  },
}

// ==================== 交易API ====================

export const tradingAPI = {
  // 获取持仓
  getPositions: async () => {
    const response = await apiClient.get('/api/v1/trading/positions')
    return response.data
  },

  // 获取订单
  getOrders: async () => {
    const response = await apiClient.get('/api/v1/trading/orders')
    return response.data
  },

  // 创建订单
  createOrder: async (order: {
    symbol: string
    side: 'buy' | 'sell'
    quantity: number
    price?: number
    order_type?: 'limit' | 'market'
  }) => {
    const response = await apiClient.post('/api/v1/trading/orders', order)
    return response.data
  },

  // 取消订单
  cancelOrder: async (orderId: string) => {
    const response = await apiClient.delete(`/api/v1/trading/orders/${orderId}`)
    return response.data
  },
}

// ==================== 策略API ====================

export const strategyAPI = {
  // 获取策略列表
  getStrategies: async () => {
    const response = await apiClient.get('/api/v1/strategies')
    return response.data
  },

  // 创建策略
  createStrategy: async (strategy: {
    name: string
    code: string
    description?: string
    parameters?: any
  }) => {
    const response = await apiClient.post('/api/v1/strategies', strategy)
    return response.data
  },

  // 更新策略
  updateStrategy: async (strategyId: string, updates: any) => {
    const response = await apiClient.put(`/api/v1/strategies/${strategyId}`, updates)
    return response.data
  },

  // 删除策略
  deleteStrategy: async (strategyId: string) => {
    const response = await apiClient.delete(`/api/v1/strategies/${strategyId}`)
    return response.data
  },

  // 运行策略
  runStrategy: async (strategyId: string) => {
    const response = await apiClient.post(`/api/v1/strategies/${strategyId}/run`)
    return response.data
  },

  // 停止策略
  stopStrategy: async (strategyId: string) => {
    const response = await apiClient.post(`/api/v1/strategies/${strategyId}/stop`)
    return response.data
  },
}

// ==================== 回测API ====================

export const backtestAPI = {
  // 运行回测
  runBacktest: async (params: {
    strategy_id: string
    start_date: string
    end_date: string
    initial_capital: number
    symbols: string[]
  }) => {
    const response = await apiClient.post('/api/v1/backtest/run', params)
    return response.data
  },

  // 获取回测结果
  getBacktestResult: async (resultId: string) => {
    const response = await apiClient.get(`/api/v1/backtest/results/${resultId}`)
    return response.data
  },

  // 获取回测历史
  getBacktestHistory: async () => {
    const response = await apiClient.get('/api/v1/backtest/history')
    return response.data
  },
}

// ==================== 风险管理API ====================

export const riskAPI = {
  // 获取风险指标
  getRiskMetrics: async () => {
    const response = await apiClient.get('/api/v1/risk/metrics')
    return response.data
  },

  // 获取风险限制
  getRiskLimits: async () => {
    const response = await apiClient.get('/api/v1/risk/limits')
    return response.data
  },

  // 更新风险限制
  updateRiskLimits: async (limits: any) => {
    const response = await apiClient.put('/api/v1/risk/limits', limits)
    return response.data
  },
}

// ==================== 监控API ====================

export const monitoringAPI = {
  // 获取系统状态
  getSystemStatus: async () => {
    const response = await apiClient.get('/api/v1/monitoring/system')
    return response.data
  },

  // 获取性能指标
  getPerformanceMetrics: async () => {
    const response = await apiClient.get('/api/v1/monitoring/performance')
    return response.data
  },
}

// ==================== WebSocket连接 ====================

export class WebSocketManager {
  private ws: WebSocket | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  private listeners: Map<string, Set<Function>> = new Map()

  constructor(private url: string = `ws://localhost:8000/api/v1/ws/market`) {}

  // 连接WebSocket
  connect() {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return
    }

    this.ws = new WebSocket(this.url)

    this.ws.onopen = () => {
      console.log('WebSocket连接成功')
      this.emit('connected', null)
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit('message', data)
        
        // 根据消息类型分发
        if (data.type) {
          this.emit(data.type, data)
        }
      } catch (error) {
        console.error('WebSocket消息解析失败:', error)
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.emit('error', error)
    }

    this.ws.onclose = () => {
      console.log('WebSocket连接关闭')
      this.emit('disconnected', null)
      this.reconnect()
    }
  }

  // 断线重连
  private reconnect() {
    if (this.reconnectTimer) {
      return
    }

    this.reconnectTimer = setTimeout(() => {
      console.log('尝试重新连接WebSocket...')
      this.reconnectTimer = null
      this.connect()
    }, 5000)
  }

  // 发送消息
  send(data: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.error('WebSocket未连接')
    }
  }

  // 订阅事件
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
  }

  // 取消订阅
  off(event: string, callback: Function) {
    this.listeners.get(event)?.delete(callback)
  }

  // 触发事件
  private emit(event: string, data: any) {
    this.listeners.get(event)?.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`事件处理器错误 [${event}]:`, error)
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

// 创建WebSocket实例
export const wsManager = new WebSocketManager()

// 导出统一的API对象
export const optimizedAPI = {
  auth: authAPI,
  market: marketAPI,
  trading: tradingAPI,
  strategy: strategyAPI,
  backtest: backtestAPI,
  risk: riskAPI,
  monitoring: monitoringAPI,
  ws: wsManager,
}

export default optimizedAPI