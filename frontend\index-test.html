<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue.js Test - 量化投资平台</title>
  <style>
    #app { 
      width: 100%; 
      min-height: 100vh; 
      background: #f5f7fa;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- Initial loading content -->
    <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
      <h1>⏳ Loading Vue.js Test...</h1>
      <p>If this message persists, Vue.js failed to load.</p>
    </div>
  </div>
  
  <script type="module" src="/src/main-simple-test.ts"></script>
  
  <!-- Error tracking -->
  <script>
    window.addEventListener('error', function(e) {
      console.error('🚨 Global Error:', e.error || e.message);
    });
    
    window.addEventListener('unhandledrejection', function(e) {
      console.error('🚨 Unhandled Promise Rejection:', e.reason);
    });
    
    console.log('🔧 Test page loaded, waiting for Vue...');
  </script>
</body>
</html>