import { ref, computed, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useTradingStore } from '@/stores/modules/trading'
import { useMarketStore } from '@/stores/modules/market'
import { usePortfolioStore } from '@/stores/modules/portfolio'
import { useRealTimeData } from '@/composables/useRealTimeData'
import { debounce } from 'lodash-es'

// 数据刷新配置
const REFRESH_CONFIG = {
  debounceTime: 1000,
  maxRetries: 3,
  retryDelay: 2000
}

export function useDashboardData() {
  // Stores
  const tradingStore = useTradingStore()
  const marketStore = useMarketStore()
  const portfolioStore = usePortfolioStore()

  // 实时数据
  const {
    isConnected,
    connectionStatus,
    connectionQuality,
    realTimeMetrics,
    priceFlashEffects,
    realtimeNews,
    getPriceFlashClass
  } = useRealTimeData()

  // 状态
  const isLoading = ref(true)
  const refreshing = ref(false)
  const error = ref<Error | null>(null)
  const retryCount = ref(0)

  // Store 响应式引用
  const { account, positions } = storeToRefs(tradingStore)
  const { indices, hotStocks, news } = storeToRefs(marketStore)
  const { portfolioTrend } = storeToRefs(portfolioStore)

  // 统一的账户指标数据（融合实时数据和Store数据）
  const accountMetrics = computed(() => {
    // 优先使用实时数据
    if (isConnected.value && realTimeMetrics.value?.lastUpdate) {
      return {
        totalAssets: realTimeMetrics.value.totalAssets ?? account.value?.totalAssets ?? 0,
        dailyProfit: realTimeMetrics.value.dailyProfit ?? account.value?.dailyProfit ?? 0,
        dailyProfitPercent: realTimeMetrics.value.dailyProfitPercent ?? account.value?.dailyProfitPercent ?? 0,
        totalProfit: realTimeMetrics.value.totalProfit ?? account.value?.totalProfit ?? 0,
        totalProfitPercent: realTimeMetrics.value.totalProfitPercent ?? account.value?.totalProfitPercent ?? 0,
        positionCount: realTimeMetrics.value.positionCount ?? positions.value?.length ?? 0,
        activeStrategies: realTimeMetrics.value.activeStrategies ?? 3,
        availableCash: realTimeMetrics.value.availableCash ?? (account.value?.totalAssets ?? 0) * 0.3,
        positionValue: realTimeMetrics.value.positionValue ?? (account.value?.totalAssets ?? 0) * 0.7,
        todayTrades: realTimeMetrics.value.todayTrades ?? 12
      }
    }

    // 回退到Store数据
    return {
      totalAssets: account.value?.totalAssets ?? 0,
      dailyProfit: account.value?.dailyProfit ?? 0,
      dailyProfitPercent: account.value?.dailyProfitPercent ?? 0,
      totalProfit: account.value?.totalProfit ?? 0,
      totalProfitPercent: account.value?.totalProfitPercent ?? 0,
      positionCount: positions.value?.length ?? 0,
      activeStrategies: 3,
      availableCash: (account.value?.totalAssets ?? 0) * 0.3,
      positionValue: (account.value?.totalAssets ?? 0) * 0.7,
      todayTrades: 12
    }
  })

  // 市场指数数据
  const marketIndices = computed(() => {
    return Object.entries(indices.value || {}).map(([symbol, data]: [string, any]) => ({
      symbol,
      name: getIndexName(symbol),
      value: data.currentPrice ?? 0,
      change: data.change ?? 0,
      changePercent: data.changePercent ?? 0
    }))
  })

  // 热门股票数据
  const topHotStocks = computed(() => {
    return (hotStocks.value || []).slice(0, 5)
  })

  // 最新资讯（融合实时和Store数据）
  const latestNews = computed(() => {
    const realtime = realtimeNews.value || []
    const stored = news.value || []
    
    // 合并并去重
    const merged = [...realtime, ...stored]
    const uniqueNews = Array.from(
      new Map(merged.map(item => [item.id, item])).values()
    )
    
    // 按时间排序并限制数量
    return uniqueNews
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10)
  })

  // 初始化数据（带重试机制）
  const initializeData = async (): Promise<void> => {
    isLoading.value = true
    error.value = null

    try {
      // 并行初始化所有Store
      await Promise.allSettled([
        tradingStore.initialize(),
        marketStore.initialize(),
        portfolioStore.initialize()
      ])

      retryCount.value = 0
      isLoading.value = false
    } catch (e) {
      error.value = e as Error
      
      // 重试机制
      if (retryCount.value < REFRESH_CONFIG.maxRetries) {
        retryCount.value++
        console.warn(`Data initialization failed, retrying... (${retryCount.value}/${REFRESH_CONFIG.maxRetries})`)
        
        setTimeout(() => {
          initializeData()
        }, REFRESH_CONFIG.retryDelay)
      } else {
        console.error('Failed to initialize dashboard data after max retries:', e)
        isLoading.value = false
      }
    }
  }

  // 刷新数据（带防抖）
  const refreshData = debounce(async (): Promise<void> => {
    if (refreshing.value) return

    refreshing.value = true
    error.value = null

    try {
      // 并行刷新所有数据
      const results = await Promise.allSettled([
        tradingStore.refresh(),
        marketStore.fetchMarketOverview(),
        marketStore.fetchHotStocks(),
        marketStore.fetchNews(),
        portfolioStore.fetchPortfolioTrend({ timeRange: 'today' })
      ])

      // 检查是否有失败的请求
      const failures = results.filter(r => r.status === 'rejected')
      if (failures.length > 0) {
        console.warn(`${failures.length} refresh requests failed`)
      }

      return Promise.resolve()
    } catch (e) {
      error.value = e as Error
      console.error('Failed to refresh data:', e)
      throw e
    } finally {
      refreshing.value = false
    }
  }, REFRESH_CONFIG.debounceTime)

  // 加载特定时间范围的数据
  const loadTimeRangeData = async (range: 'today' | 'week' | 'month'): Promise<void> => {
    try {
      await portfolioStore.fetchPortfolioTrend({ 
        timeRange: range,
        symbol: 'portfolio'
      })
    } catch (e) {
      console.error(`Failed to load ${range} data:`, e)
      error.value = e as Error
    }
  }

  // 清理函数
  const cleanup = (): void => {
    // 取消防抖
    refreshData.cancel()
    
    // 清理Store订阅等
    isLoading.value = false
    refreshing.value = false
    error.value = null
    retryCount.value = 0
  }

  // 工具函数
  const getIndexName = (symbol: string): string => {
    const nameMap: Record<string, string> = {
      'SH000001': '上证指数',
      'SZ399001': '深证成指',
      'SZ399006': '创业板指',
      'SH000688': '科创50'
    }
    return nameMap[symbol] || symbol
  }

  // 生命周期
  onMounted(() => {
    initializeData()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    isLoading,
    refreshing,
    error,
    
    // 连接状态
    isConnected,
    connectionStatus,
    connectionQuality,
    
    // 数据
    accountMetrics,
    marketIndices,
    topHotStocks,
    latestNews,
    positions,
    portfolioTrend,
    
    // 方法
    refreshData,
    loadTimeRangeData,
    getPriceFlashClass,
    
    // 工具
    getIndexName
  }
}