/**
 * Simplified Vue App for Testing
 * This version removes complex dependencies to isolate the mounting issue
 */

import { createApp } from 'vue'

// Simple test component
const SimpleApp = {
  template: `
    <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
      <h1 style="color: #409eff;">🚀 Vue App Test</h1>
      <p style="color: #666; margin: 20px 0;">Vue.js is successfully running!</p>
      <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Current Time: {{ currentTime }}</h3>
        <p>Counter: {{ counter }}</p>
        <button @click="increment" style="background: #409eff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
          Click me! ({{ counter }})
        </button>
      </div>
      <div style="margin-top: 20px;">
        <h4>Test Results:</h4>
        <ul style="text-align: left; display: inline-block;">
          <li>✅ Vue.js createApp: Working</li>
          <li>✅ Reactive data: {{ counter > 0 ? 'Working' : 'Not tested' }}</li>
          <li>✅ Template rendering: Working</li>
          <li>✅ Event handling: {{ counter > 0 ? 'Working' : 'Click button to test' }}</li>
          <li>✅ App mounting: Working (you can see this!)</li>
        </ul>
      </div>
    </div>
  `,
  data() {
    return {
      counter: 0,
      currentTime: new Date().toLocaleString()
    }
  },
  mounted() {
    console.log('✅ Vue component mounted successfully')
    this.updateTime()
  },
  methods: {
    increment() {
      this.counter++
      console.log('✅ Vue reactivity working, counter:', this.counter)
    },
    updateTime() {
      setInterval(() => {
        this.currentTime = new Date().toLocaleString()
      }, 1000)
    }
  }
}

try {
  console.log('🚀 Starting simplified Vue app...')
  
  // Create Vue application
  const app = createApp(SimpleApp)
  
  // Global error handler
  app.config.errorHandler = (err, instance, info) => {
    console.error('❌ Vue Error:', err)
    console.error('Component instance:', instance)
    console.error('Error info:', info)
  }
  
  // Mount application
  const mountResult = app.mount('#app')
  
  console.log('✅ Vue app mounted successfully:', mountResult)
  console.log('📊 App element content length:', document.getElementById('app')?.innerHTML?.length)
  
  // Export app for debugging
  if (import.meta.env.DEV) {
    (window as any).__SIMPLE_VUE_APP__ = app
  }
  
} catch (error) {
  console.error('❌ Failed to create or mount Vue app:', error)
  
  // Fallback: Show error in the app element
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
        <h1 style="color: #f56c6c;">❌ Vue App Failed to Load</h1>
        <p style="color: #666;">Error: ${error.message}</p>
        <div style="background: #fef0f0; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
          <h3>Error Details:</h3>
          <pre style="font-size: 12px; overflow-x: auto;">${error.stack}</pre>
        </div>
        <p>This indicates an issue with Vue.js initialization or dependencies.</p>
      </div>
    `
  }
}