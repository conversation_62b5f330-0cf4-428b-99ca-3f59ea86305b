"""
WebSocket连接管理器
优化的实时数据推送功能
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Set, Any, Optional
from enum import Enum
import uuid

from fastapi import WebSocket, WebSocketDisconnect
from starlette.websockets import WebSocketState
from loguru import logger


class MessageType(str, Enum):
    """消息类型"""
    MARKET_DATA = "market_data"
    ORDER_UPDATE = "order_update"
    POSITION_UPDATE = "position_update"
    STRATEGY_UPDATE = "strategy_update"
    SYSTEM_ALERT = "system_alert"
    HEARTBEAT = "heartbeat"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"
    ERROR = "error"

class ConnectionManager:
    """优化的WebSocket连接管理器"""

    def __init__(self):
        # 活跃连接
        self.active_connections: Dict[str, WebSocket] = {}

        # 订阅管理 - 统一变量命名
        self.symbol_subscribers: Dict[str, Set[str]] = {}  # symbol -> client_ids
        self.client_subscriptions: Dict[str, Set[str]] = {}  # client_id -> symbols

        # 连接元数据
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}

        # 消息队列
        self.message_queue: asyncio.Queue = asyncio.Queue()

        # 统计信息
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "errors": 0
        }

        # 后台任务
        self._message_processor_task = None
        self._heartbeat_task = None

        logger.info("WebSocket连接管理器初始化完成")
        
    async def connect(self, client_id: str, websocket: WebSocket):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.client_subscriptions[client_id] = set()
        self.stats["total_connections"] += 1
        
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
        # 发送欢迎消息
        await self.send_personal_message(
            {
                "type": "welcome",
                "message": "欢迎连接到量化投资平台实时数据服务",
                "timestamp": datetime.now().isoformat()
            },
            client_id
        )
    
    async def disconnect(self, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            # 清理订阅信息
            if client_id in self.client_subscriptions:
                for symbol in self.client_subscriptions[client_id]:
                    if symbol in self.symbol_subscribers:
                        self.symbol_subscribers[symbol].discard(client_id)
                        if not self.symbol_subscribers[symbol]:
                            del self.symbol_subscribers[symbol]
                del self.client_subscriptions[client_id]
            
            # 移除连接
            del self.active_connections[client_id]
            
            logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """发送消息给特定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_json(message)
                    self.stats["messages_sent"] += 1
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.stats["errors"] += 1
                await self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """广播消息给所有连接的客户端"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_json(message)
                    self.stats["messages_sent"] += 1
                else:
                    disconnected_clients.append(client_id)
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                self.stats["errors"] += 1
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
    
    async def broadcast_to_symbol_subscribers(self, symbol: str, message: dict):
        """向订阅特定股票的客户端广播消息"""
        if symbol not in self.symbol_subscribers:
            return
        
        disconnected_clients = []
        
        for client_id in self.symbol_subscribers[symbol]:
            if client_id in self.active_connections:
                try:
                    await self.send_personal_message(message, client_id)
                except Exception as e:
                    logger.error(f"Error sending to subscriber {client_id}: {e}")
                    disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            await self.disconnect(client_id)
    
    async def subscribe_client_to_symbols(self, client_id: str, symbols: List[str]):
        """为客户端订阅股票"""
        if client_id not in self.active_connections:
            return False
        
        # 更新客户端订阅
        self.client_subscriptions[client_id].update(symbols)
        
        # 更新股票订阅者
        for symbol in symbols:
            if symbol not in self.symbol_subscribers:
                self.symbol_subscribers[symbol] = set()
            self.symbol_subscribers[symbol].add(client_id)
        
        # 发送订阅确认
        await self.send_personal_message(
            {
                "type": "subscription_confirmed",
                "symbols": symbols,
                "timestamp": datetime.now().isoformat()
            },
            client_id
        )
        
        logger.info(f"Client {client_id} subscribed to: {symbols}")
        return True
    
    async def unsubscribe_client_from_symbols(self, client_id: str, symbols: List[str]):
        """取消客户端的股票订阅"""
        if client_id not in self.active_connections:
            return False
        
        # 更新客户端订阅
        for symbol in symbols:
            self.client_subscriptions[client_id].discard(symbol)
            
            # 更新股票订阅者
            if symbol in self.symbol_subscribers:
                self.symbol_subscribers[symbol].discard(client_id)
                if not self.symbol_subscribers[symbol]:
                    del self.symbol_subscribers[symbol]
        
        # 发送取消订阅确认
        await self.send_personal_message(
            {
                "type": "unsubscription_confirmed",
                "symbols": symbols,
                "timestamp": datetime.now().isoformat()
            },
            client_id
        )
        
        logger.info(f"Client {client_id} unsubscribed from: {symbols}")
        return True
    
    async def handle_client_message(self, client_id: str, data: dict):
        """处理客户端消息"""
        self.stats["messages_received"] += 1
        
        message_type = data.get("type")
        
        if message_type == "subscribe":
            symbols = data.get("symbols", [])
            await self.subscribe_client_to_symbols(client_id, symbols)
            
        elif message_type == "unsubscribe":
            symbols = data.get("symbols", [])
            await self.unsubscribe_client_from_symbols(client_id, symbols)
            
        elif message_type == "ping":
            await self.send_personal_message(
                {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                },
                client_id
            )
            
        else:
            await self.send_personal_message(
                {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.now().isoformat()
                },
                client_id
            )
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            **self.stats,
            "active_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.client_subscriptions.values()),
            "subscribed_symbols": len(self.symbol_subscribers)
        }
    
    def get_subscribed_symbols(self) -> List[str]:
        """获取所有被订阅的股票列表"""
        return list(self.symbol_subscribers.keys())
    
    def get_client_info(self) -> List[dict]:
        """获取所有客户端信息"""
        return [
            {
                "client_id": client_id,
                "subscriptions": list(self.client_subscriptions.get(client_id, set()))
            }
            for client_id in self.active_connections
        ]

# 全局连接管理器实例
manager = ConnectionManager()


class RealtimeDataPusher:
    """实时数据推送服务"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
        self.running = False
        self._tasks = []
        
    async def start(self):
        """启动实时数据推送服务"""
        self.running = True
        
        # 启动不同的数据推送任务
        self._tasks = [
            asyncio.create_task(self._push_quotes()),
            asyncio.create_task(self._push_orderbook()),
            asyncio.create_task(self._push_trades()),
        ]
        
        logger.info("Realtime data pusher started")
    
    async def stop(self):
        """停止实时数据推送服务"""
        self.running = False
        
        # 取消所有任务
        for task in self._tasks:
            task.cancel()
        
        # 等待任务结束
        await asyncio.gather(*self._tasks, return_exceptions=True)
        
        logger.info("Realtime data pusher stopped")
    
    async def _push_quotes(self):
        """推送实时行情"""
        from app.services.enhanced_market_service import enhanced_market_service
        
        while self.running:
            try:
                # 获取所有被订阅的股票
                symbols = self.manager.get_subscribed_symbols()
                
                if symbols:
                    # 批量获取行情数据
                    quotes = await enhanced_market_service.get_quotes(symbols[:50])  # 限制一次最多50个
                    
                    # 推送给订阅者
                    for quote in quotes:
                        message = {
                            "type": "quote",
                            "data": {
                                "symbol": quote.symbol,
                                "name": quote.name,
                                "price": quote.currentPrice,
                                "change": quote.change,
                                "changePercent": quote.changePercent,
                                "volume": quote.volume,
                                "turnover": quote.turnover,
                                "high": quote.high,
                                "low": quote.low,
                                "open": quote.open,
                                "time": datetime.now().isoformat()
                            }
                        }
                        
                        await self.manager.broadcast_to_symbol_subscribers(
                            quote.symbol, 
                            message
                        )
                
                # 每秒推送一次
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error pushing quotes: {e}")
                await asyncio.sleep(5)
    
    async def _push_orderbook(self):
        """推送订单簿数据"""
        from app.services.enhanced_market_service import enhanced_market_service
        
        while self.running:
            try:
                # 获取所有被订阅的股票
                symbols = self.manager.get_subscribed_symbols()
                
                if symbols:
                    # 为每个股票推送订单簿（模拟数据）
                    for symbol in symbols[:20]:  # 限制一次最多20个
                        # 生成模拟订单簿数据
                        import random
                        base_price = 100 + random.uniform(-10, 10)
                        
                        orderbook = {
                            "type": "orderbook",
                            "data": {
                                "symbol": symbol,
                                "bids": [
                                    {
                                        "price": base_price - i * 0.01,
                                        "volume": random.randint(100, 1000) * 100
                                    }
                                    for i in range(1, 6)
                                ],
                                "asks": [
                                    {
                                        "price": base_price + i * 0.01,
                                        "volume": random.randint(100, 1000) * 100
                                    }
                                    for i in range(1, 6)
                                ],
                                "time": datetime.now().isoformat()
                            }
                        }
                        
                        await self.manager.broadcast_to_symbol_subscribers(
                            symbol,
                            orderbook
                        )
                
                # 每2秒推送一次
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error pushing orderbook: {e}")
                await asyncio.sleep(5)
    
    async def _push_trades(self):
        """推送成交数据"""
        while self.running:
            try:
                # 获取所有被订阅的股票
                symbols = self.manager.get_subscribed_symbols()
                
                if symbols:
                    # 为活跃股票推送成交数据（模拟）
                    import random
                    
                    # 随机选择一些股票
                    active_symbols = random.sample(
                        symbols, 
                        min(5, len(symbols))
                    )
                    
                    for symbol in active_symbols:
                        # 生成模拟成交数据
                        trades = []
                        for _ in range(random.randint(1, 5)):
                            trades.append({
                                "price": 100 + random.uniform(-5, 5),
                                "volume": random.randint(100, 1000) * 100,
                                "direction": random.choice(["buy", "sell"]),
                                "time": datetime.now().isoformat()
                            })
                        
                        message = {
                            "type": "trades",
                            "data": {
                                "symbol": symbol,
                                "trades": trades
                            }
                        }
                        
                        await self.manager.broadcast_to_symbol_subscribers(
                            symbol,
                            message
                        )
                
                # 每3秒推送一次
                await asyncio.sleep(3)
                
            except Exception as e:
                logger.error(f"Error pushing trades: {e}")
                await asyncio.sleep(5)

# 全局实时数据推送器实例
realtime_pusher = RealtimeDataPusher(manager)