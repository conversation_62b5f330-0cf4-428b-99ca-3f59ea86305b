<template>
  <div ref="containerRef" class="virtual-scroll-container" @scroll="handleScroll">
    <!-- 占位元素，撑起滚动条 -->
    <div class="virtual-scroll-phantom" :style="{ height: totalHeight + 'px' }"></div>
    
    <!-- 实际渲染的列表 -->
    <div 
      class="virtual-scroll-list"
      :style="{ transform: `translateY(${offset}px)` }"
    >
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item)"
        class="virtual-scroll-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot name="item" :item="item" :index="getItemIndex(item)">
          {{ item }}
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { throttle } from 'lodash-es'

interface Props {
  // 数据列表
  items: T[]
  // 每项高度
  itemHeight?: number
  // 缓冲区大小（渲染视口外的项数）
  buffer?: number
  // 容器高度
  height?: number | string
  // 获取项的唯一key
  keyField?: string | ((item: T) => string | number)
  // 是否启用滚动到底部加载更多
  enableLoadMore?: boolean
  // 加载更多的阈值（距离底部多少px时触发）
  loadMoreThreshold?: number
  // 是否正在加载
  loading?: boolean
}

interface Emits {
  (e: 'load-more'): void
  (e: 'scroll', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 50,
  buffer: 5,
  height: 400,
  enableLoadMore: false,
  loadMoreThreshold: 100,
  loading: false
})

const emit = defineEmits<Emits>()

// 容器引用
const containerRef = ref<HTMLElement>()

// 滚动状态
const scrollTop = ref(0)
const containerHeight = ref(0)

// 计算属性
const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleCount = computed(() => {
  return Math.ceil(containerHeight.value / props.itemHeight) + props.buffer * 2
})

const startIndex = computed(() => {
  const start = Math.floor(scrollTop.value / props.itemHeight) - props.buffer
  return Math.max(0, start)
})

const endIndex = computed(() => {
  const end = startIndex.value + visibleCount.value
  return Math.min(props.items.length, end)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value)
})

const offset = computed(() => {
  return startIndex.value * props.itemHeight
})

// 获取容器高度样式
const containerStyle = computed(() => {
  if (typeof props.height === 'number') {
    return { height: `${props.height}px` }
  }
  return { height: props.height }
})

// 方法
const getItemKey = (item: T): string | number => {
  if (!props.keyField) {
    return props.items.indexOf(item)
  }
  
  if (typeof props.keyField === 'function') {
    return props.keyField(item)
  }
  
  return (item as any)[props.keyField]
}

const getItemIndex = (item: T): number => {
  return props.items.indexOf(item)
}

// 滚动处理（节流优化）
const handleScroll = throttle((event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  emit('scroll', event)
  
  // 检查是否需要加载更多
  if (props.enableLoadMore && !props.loading) {
    const scrollBottom = target.scrollHeight - target.scrollTop - target.clientHeight
    if (scrollBottom <= props.loadMoreThreshold) {
      emit('load-more')
    }
  }
}, 16) // 约60fps

// 滚动到指定索引
const scrollToIndex = (index: number, behavior: ScrollBehavior = 'smooth') => {
  if (!containerRef.value) return
  
  const offset = index * props.itemHeight
  containerRef.value.scrollTo({
    top: offset,
    behavior
  })
}

// 滚动到顶部
const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  scrollToIndex(0, behavior)
}

// 滚动到底部
const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
  scrollToIndex(props.items.length - 1, behavior)
}

// 刷新滚动位置
const refreshScrollPosition = async () => {
  await nextTick()
  if (containerRef.value) {
    scrollTop.value = containerRef.value.scrollTop
  }
}

// 监听容器高度变化
const resizeObserver = ref<ResizeObserver>()

onMounted(() => {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight
    
    // 监听容器大小变化
    resizeObserver.value = new ResizeObserver((entries) => {
      for (const entry of entries) {
        containerHeight.value = entry.contentRect.height
      }
    })
    resizeObserver.value.observe(containerRef.value)
  }
})

onUnmounted(() => {
  resizeObserver.value?.disconnect()
})

// 监听数据变化，保持滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  // 如果是加载更多，保持滚动位置
  if (newLength > oldLength && props.enableLoadMore) {
    refreshScrollPosition()
  }
})

// 暴露方法
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  refreshScrollPosition
})
</script>

<style scoped>
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  height: v-bind(containerStyle.height);
}

.virtual-scroll-phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}

.virtual-scroll-list {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  z-index: 1;
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  padding: 0;
  box-sizing: border-box;
}

/* 滚动条样式优化 */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.virtual-scroll-container.loading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.9), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
</style>