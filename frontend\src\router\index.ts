import { createRouter, createWebHistory } from 'vue-router'
import { h } from 'vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 主布局路由
    {
      path: '/',
      component: () => import('@/layouts/ProfessionalLayout.vue'),
      redirect: '/dashboard',
      children: [
        // Dashboard路由
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('@/views/Dashboard/DashboardView.vue'),
          meta: {
            title: '投资仪表盘',
            icon: 'DataAnalysis',
            requiresAuth: false
          }
        },
        // 市场路由
        {
          path: 'market',
          name: 'market',
          component: () => import('@/views/Market/MarketView.vue'),
          meta: {
            title: '市场行情',
            icon: 'TrendCharts',
            requiresAuth: false
          }
        },
        // 交易路由
        {
          path: 'trading',
          name: 'trading',
          component: () => import('@/views/Trading/TradingView.vue'),
          meta: {
            title: '交易功能',
            icon: 'Money',
            requiresAuth: false
          }
        },
        // 策略路由
        {
          path: 'strategy',
          name: 'strategy',
          component: () => import('@/views/Strategy/StrategyView.vue'),
          meta: {
            title: '策略管理',
            icon: 'DataBoard',
            requiresAuth: false
          }
        },
        // 投资组合路由
        {
          path: 'portfolio',
          name: 'portfolio',
          component: () => import('@/views/Portfolio/PortfolioView.vue'),
          meta: {
            title: '投资组合',
            icon: 'PieChart',
            requiresAuth: false
          }
        },
        // 回测路由
        {
          path: 'backtest',
          name: 'backtest',
          component: () => import('@/views/Backtest/BacktestView.vue'),
          meta: {
            title: '回测分析',
            icon: 'DataAnalysis',
            requiresAuth: false
          }
        },
        // 风险管理路由
        {
          path: 'risk',
          name: 'risk',
          component: () => import('@/views/Risk/RiskView.vue'),
          meta: {
            title: '风险管理',
            icon: 'Warning',
            requiresAuth: false
          }
        },
        // 演示路由
        {
          path: 'demo',
          name: 'demo',
          component: () => import('@/views/Demo/DemoView.vue'),
          meta: {
            title: '功能演示',
            icon: 'VideoPlay',
            requiresAuth: false
          }
        },
        // 测试路由
        {
          path: 'test',
          name: 'test',
          component: () => import('@/views/Test/ComponentTest.vue'),
          meta: {
            title: '组件测试',
            icon: 'Setting',
            requiresAuth: false
          }
        },
      ],
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => {
        return import('@/views/Error/404.vue').catch(() => {
          // 如果404页面不存在，返回简单的404组件
          return {
            render() {
              return h('div', { style: 'text-align: center; padding: 2rem;' }, [
                h('h1', '404 - 页面未找到'),
                h('p', '抱歉，您访问的页面不存在')
              ])
            }
          }
        })
      },
      meta: {
        title: '页面未找到',
        requiresAuth: false,
      },
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router

