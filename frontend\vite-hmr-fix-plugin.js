/**
 * Vite HMR Fix Plugin
 * Handles the "Cannot read properties of undefined (reading 'on')" error in Vite 6.x
 */

export function hmrFixPlugin() {
  return {
    name: 'hmr-fix',
    configureServer(server) {
      // Override HMR handling
      const originalSend = server.ws.send;
      server.ws.send = function(payload) {
        try {
          return originalSend.call(this, payload);
        } catch (error) {
          console.warn('HMR send error (suppressed):', error.message);
        }
      };
    },
    transform(code, id) {
      // Inject HMR guard at the top of Vue files
      if (id.endsWith('.vue') && code.includes('import.meta.hot')) {
        const hmrGuard = `
if (import.meta.hot && typeof import.meta.hot.on !== 'function') {
  import.meta.hot.on = function() {};
}
`;
        return hmrGuard + code;
      }
      return code;
    }
  };
}