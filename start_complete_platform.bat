@echo off
echo ========================================
echo   量化投资平台完整启动脚本
echo ========================================
echo.

:: 检查Python
echo [1/5] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

:: 检查Node.js
echo [2/5] 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js 14+
    pause
    exit /b 1
)

:: 安装后端依赖
echo [3/5] 安装后端依赖...
cd backend
pip install fastapi uvicorn aiofiles psutil websocket-client requests -q
if errorlevel 1 (
    echo 警告: 部分依赖安装失败，但继续运行
)

:: 启动后端
echo [4/5] 启动后端服务...
start "量化平台后端" cmd /k "python app/main_optimized.py"

:: 等待后端启动
echo 等待后端启动...
timeout /t 5 /nobreak >nul

:: 检查后端是否启动成功
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo 警告: 后端可能未完全启动，请检查8000端口
) else (
    echo 后端启动成功！
)

:: 启动前端
echo [5/5] 启动前端服务...
cd ..\frontend

:: 检查是否需要安装依赖
if not exist "node_modules" (
    echo 首次运行，安装前端依赖...
    npm install
)

:: 启动前端开发服务器
echo 启动前端开发服务器...
start "量化平台前端" cmd /k "npm run dev"

:: 等待前端启动
echo 等待前端启动...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo   平台启动完成！
echo ========================================
echo.
echo 访问地址:
echo   前端: http://localhost:5173
echo   后端API: http://localhost:8000
echo   API文档: http://localhost:8000/docs
echo.
echo 默认账号:
echo   管理员: admin / admin
echo   演示: demo / demo
echo.
echo 按任意键打开浏览器...
pause >nul

:: 打开浏览器
start http://localhost:5173

echo.
echo 提示: 
echo - 关闭此窗口不会停止服务
echo - 使用 stop_all.bat 停止所有服务
echo.
pause