<template>
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <h1 class="page-title">{{ title }}</h1>
        <ConnectionStatus 
          :status="connectionStatus"
          :quality="connectionQuality"
        />
      </div>

      <div class="header-actions">
        <el-button-group>
          <el-button
            v-for="range in timeRanges"
            :key="range.value"
            :type="currentTimeRange === range.value ? 'primary' : 'default'"
            size="small"
            @click="$emit('time-range-change', range.value)"
          >
            {{ range.label }}
          </el-button>
        </el-button-group>

        <el-button
          type="primary"
          @click="$emit('refresh')"
          :loading="refreshing"
        >
          刷新数据
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ConnectionStatus from './ConnectionStatus.vue'

interface Props {
  title?: string
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error'
  connectionQuality?: 'excellent' | 'good' | 'poor'
  currentTimeRange: 'today' | 'week' | 'month'
  refreshing: boolean
}

interface Emits {
  (e: 'time-range-change', value: 'today' | 'week' | 'month'): void
  (e: 'refresh'): void
}

withDefaults(defineProps<Props>(), {
  title: '投资仪表盘'
})

defineEmits<Emits>()

const timeRanges = [
  { value: 'today', label: '今日' },
  { value: 'week', label: '本周' },
  { value: 'month', label: '本月' }
]
</script>

<style scoped>
.dashboard-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}
</style>