<template>
  <el-card class="positions-card">
    <template #header>
      <div class="card-header">
        <h3>持仓概览</h3>
        <div class="header-actions">
          <el-input
            v-model="searchText"
            placeholder="搜索股票代码或名称"
            :prefix-icon="Search"
            size="small"
            clearable
            style="width: 200px"
          />
          <el-button text size="small" @click="exportPositions">
            导出 <el-icon><Download /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <!-- 使用虚拟滚动列表 -->
    <VirtualScrollList
      v-if="filteredPositions.length > 0"
      :items="filteredPositions"
      :item-height="60"
      :height="400"
      :buffer="3"
      key-field="symbol"
    >
      <template #item="{ item: position }">
        <div class="position-item">
          <div class="position-info">
            <div class="stock-symbol">{{ position.symbol }}</div>
            <div class="stock-name">{{ position.name }}</div>
          </div>
          
          <div class="position-metrics">
            <div class="metric">
              <span class="label">数量</span>
              <span class="value">{{ position.quantity }}</span>
            </div>
            <div class="metric">
              <span class="label">成本</span>
              <span class="value">¥{{ position.avgCost.toFixed(2) }}</span>
            </div>
            <div class="metric">
              <span class="label">现价</span>
              <span class="value">¥{{ position.currentPrice.toFixed(2) }}</span>
            </div>
            <div class="metric">
              <span class="label">市值</span>
              <span class="value">¥{{ formatNumber(position.marketValue) }}</span>
            </div>
          </div>
          
          <div class="position-pnl" :class="position.pnl >= 0 ? 'profit' : 'loss'">
            <div class="pnl-amount">
              {{ position.pnl >= 0 ? '+' : '' }}¥{{ formatNumber(Math.abs(position.pnl)) }}
            </div>
            <div class="pnl-percent">
              {{ position.pnlPercent >= 0 ? '+' : '' }}{{ position.pnlPercent.toFixed(2) }}%
            </div>
          </div>
          
          <div class="position-actions">
            <el-button text size="small" @click="handleTrade(position, 'buy')">
              加仓
            </el-button>
            <el-button text size="small" @click="handleTrade(position, 'sell')">
              减仓
            </el-button>
          </div>
        </div>
      </template>
    </VirtualScrollList>

    <!-- 空状态 -->
    <el-empty v-else-if="!loading" description="暂无持仓数据" />

    <!-- 加载状态 -->
    <div v-else class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 统计信息 -->
    <div class="positions-summary" v-if="positions.length > 0">
      <div class="summary-item">
        <span class="label">总持仓</span>
        <span class="value">{{ positions.length }}只</span>
      </div>
      <div class="summary-item">
        <span class="label">总市值</span>
        <span class="value">¥{{ formatNumber(totalMarketValue) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">总盈亏</span>
        <span class="value" :class="totalPnl >= 0 ? 'profit' : 'loss'">
          {{ totalPnl >= 0 ? '+' : '' }}¥{{ formatNumber(Math.abs(totalPnl)) }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">盈亏比例</span>
        <span class="value" :class="totalPnlPercent >= 0 ? 'profit' : 'loss'">
          {{ totalPnlPercent >= 0 ? '+' : '' }}{{ totalPnlPercent.toFixed(2) }}%
        </span>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Search, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import VirtualScrollList from '@/components/common/VirtualScrollList.vue'

interface Position {
  symbol: string
  name: string
  quantity: number
  avgCost: number
  currentPrice: number
  marketValue: number
  pnl: number
  pnlPercent: number
}

interface Props {
  positions: Position[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 搜索
const searchText = ref('')

// 过滤后的持仓列表
const filteredPositions = computed(() => {
  if (!searchText.value) {
    return props.positions
  }
  
  const keyword = searchText.value.toLowerCase()
  return props.positions.filter(position => 
    position.symbol.toLowerCase().includes(keyword) ||
    position.name.toLowerCase().includes(keyword)
  )
})

// 统计数据
const totalMarketValue = computed(() => {
  return props.positions.reduce((sum, p) => sum + p.marketValue, 0)
})

const totalPnl = computed(() => {
  return props.positions.reduce((sum, p) => sum + p.pnl, 0)
})

const totalPnlPercent = computed(() => {
  const totalCost = props.positions.reduce((sum, p) => sum + (p.avgCost * p.quantity), 0)
  if (totalCost === 0) return 0
  return (totalPnl.value / totalCost) * 100
})

// 格式化数字
const formatNumber = (value: number): string => {
  if (Math.abs(value) >= 10000) {
    return (value / 10000).toFixed(2) + '万'
  }
  return value.toFixed(2)
}

// 交易操作
const handleTrade = (position: Position, type: 'buy' | 'sell') => {
  ElMessage.info(`${type === 'buy' ? '加仓' : '减仓'} ${position.symbol} ${position.name}`)
  // TODO: 打开交易面板
}

// 导出持仓
const exportPositions = () => {
  ElMessage.success('正在导出持仓数据...')
  // TODO: 实现导出功能
}
</script>

<style scoped>
.positions-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.position-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.position-item:hover {
  background-color: #f5f7fa;
}

.position-info {
  flex: 0 0 150px;
}

.stock-symbol {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stock-name {
  font-size: 12px;
  color: #909399;
}

.position-metrics {
  flex: 1;
  display: flex;
  gap: 40px;
  padding: 0 20px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric .label {
  font-size: 12px;
  color: #909399;
}

.metric .value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.position-pnl {
  flex: 0 0 120px;
  text-align: right;
}

.pnl-amount {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.pnl-percent {
  font-size: 12px;
}

.position-pnl.profit {
  color: #67c23a;
}

.position-pnl.loss {
  color: #f56c6c;
}

.position-actions {
  flex: 0 0 100px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.loading-container {
  padding: 20px;
}

.positions-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background: #f5f7fa;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  font-size: 12px;
  color: #909399;
}

.summary-item .value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.summary-item .value.profit {
  color: #67c23a;
}

.summary-item .value.loss {
  color: #f56c6c;
}
</style>