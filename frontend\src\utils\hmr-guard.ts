/**
 * HMR Guard for Vite 6.x compatibility
 * Prevents "Cannot read properties of undefined (reading 'on')" errors
 */

export function initializeHMRGuard() {
  // Ensure import.meta exists and has proper structure
  if (typeof import !== 'undefined' && import.meta && !import.meta.hot && import.meta.env?.DEV) {
    // Create a minimal HMR context to prevent errors
    Object.defineProperty(import.meta, 'hot', {
      value: {
        accept: () => {},
        dispose: () => {},
        decline: () => {},
        invalidate: () => {},
        on: () => {},
        send: () => {},
        data: {}
      },
      writable: false,
      enumerable: true,
      configurable: false
    });
  }
}

// Auto-initialize in development
if (typeof import !== 'undefined' && import.meta?.env?.DEV) {
  initializeHMRGuard();
}