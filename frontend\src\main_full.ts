/**
 * Quantum Investment Platform - Main Entry Point
 * Full Vue 3 Application with Router, Pinia, and Element Plus
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
import router from './router'

// Import global styles
import './assets/styles/index.css'

// Create Vue application
const app = createApp(App)

// Create Pinia store
const pinia = createPinia()

// Use plugins
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: 'zh-cn'
})

// Register all Element Plus icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Configure global properties
app.config.globalProperties.$apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// Global error handler
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
  
  // You can send error to logging service here
  if (import.meta.env.PROD) {
    // Log to external service in production
  }
}

// Performance monitoring
if (import.meta.env.PROD) {
  app.config.performance = true
}

// Mount application
app.mount('#app')

// Log successful initialization
console.log('✅ Quantum Investment Platform initialized successfully')
console.log('📊 Version:', import.meta.env.VITE_APP_VERSION || '1.0.0')
console.log('🌐 API URL:', import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
console.log('🚀 Environment:', import.meta.env.MODE)

// Export app instance for debugging
if (import.meta.env.DEV) {
  window.__VUE_APP__ = app
}