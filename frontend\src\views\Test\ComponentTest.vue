<template>
  <div class="component-test">
    <h1>组件测试页面</h1>
    
    <!-- 测试基本连接 -->
    <el-card class="test-card">
      <template #header>
        <h3>系统状态检查</h3>
      </template>
      <div class="status-list">
        <div class="status-item">
          <span>Vue版本:</span>
          <el-tag type="success">{{ vueVersion }}</el-tag>
        </div>
        <div class="status-item">
          <span>Element Plus:</span>
          <el-tag type="success">已加载</el-tag>
        </div>
        <div class="status-item">
          <span>Router:</span>
          <el-tag :type="routerStatus ? 'success' : 'danger'">
            {{ routerStatus ? '正常' : '异常' }}
          </el-tag>
        </div>
        <div class="status-item">
          <span>API连接:</span>
          <el-tag :type="apiStatus ? 'success' : 'danger'">
            {{ apiStatus ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 测试路由导航 -->
    <el-card class="test-card">
      <template #header>
        <h3>路由导航测试</h3>
      </template>
      <div class="route-buttons">
        <el-button @click="navigateTo('/dashboard')">仪表盘</el-button>
        <el-button @click="navigateTo('/market')">市场</el-button>
        <el-button @click="navigateTo('/trading')">交易</el-button>
        <el-button @click="navigateTo('/strategy')">策略</el-button>
        <el-button @click="navigateTo('/portfolio')">投资组合</el-button>
      </div>
    </el-card>

    <!-- 测试API调用 -->
    <el-card class="test-card">
      <template #header>
        <h3>API测试</h3>
      </template>
      <el-button type="primary" @click="testAPI">测试API连接</el-button>
      <div v-if="apiResponse" class="api-response">
        <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
      </div>
    </el-card>

    <!-- 测试组件加载 -->
    <el-card class="test-card">
      <template #header>
        <h3>组件加载测试</h3>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="图表组件" name="chart">
          <div style="height: 300px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;">
            <span>图表组件占位</span>
          </div>
        </el-tab-pane>
        <el-tab-pane label="表格组件" name="table">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="value" label="值" />
            <el-table-column prop="status" label="状态" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { version as vueVersion } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const router = useRouter()
const routerStatus = ref(true)
const apiStatus = ref(false)
const apiResponse = ref(null)
const activeTab = ref('chart')

const tableData = ref([
  { name: '测试数据1', value: 100, status: '正常' },
  { name: '测试数据2', value: 200, status: '正常' },
  { name: '测试数据3', value: 300, status: '警告' }
])

const navigateTo = (path: string) => {
  router.push(path)
  ElMessage.success(`导航到: ${path}`)
}

const testAPI = async () => {
  try {
    const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'}/health`)
    apiStatus.value = true
    apiResponse.value = response.data
    ElMessage.success('API连接成功!')
  } catch (error) {
    apiStatus.value = false
    apiResponse.value = { error: error.message }
    ElMessage.error('API连接失败: ' + error.message)
  }
}

onMounted(() => {
  console.log('Component Test Page Mounted')
  console.log('Environment:', import.meta.env.MODE)
  console.log('API URL:', import.meta.env.VITE_API_BASE_URL)
})
</script>

<style scoped>
.component-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-item span:first-child {
  width: 120px;
  font-weight: 500;
}

.route-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.api-response {
  margin-top: 20px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.api-response pre {
  margin: 0;
  font-size: 12px;
}
</style>