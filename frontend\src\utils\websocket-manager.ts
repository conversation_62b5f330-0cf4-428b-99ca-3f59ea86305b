/**
 * WebSocket管理器 - 提供自动重连和心跳机制
 */

export interface WebSocketConfig {
  url: string
  protocols?: string | string[]
  reconnect?: boolean
  reconnectInterval?: number
  reconnectMaxRetries?: number
  heartbeatInterval?: number
  debug?: boolean
}

export interface WebSocketMessage {
  type: string
  data?: any
  timestamp?: string
  [key: string]: any
}

type MessageHandler = (message: WebSocketMessage) => void
type EventHandler = (event: any) => void

export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private isIntentionallyClosed = false
  private messageQueue: WebSocketMessage[] = []
  
  // 事件处理器
  private handlers: {
    message: Set<MessageHandler>
    open: Set<EventHandler>
    close: Set<EventHandler>
    error: Set<EventHandler>
    reconnect: Set<EventHandler>
  } = {
    message: new Set(),
    open: new Set(),
    close: new Set(),
    error: new Set(),
    reconnect: new Set()
  }
  
  constructor(config: WebSocketConfig) {
    this.config = {
      url: config.url,
      protocols: config.protocols,
      reconnect: config.reconnect ?? true,
      reconnectInterval: config.reconnectInterval ?? 3000,
      reconnectMaxRetries: config.reconnectMaxRetries ?? 10,
      heartbeatInterval: config.heartbeatInterval ?? 30000,
      debug: config.debug ?? false
    }
  }
  
  /**
   * 连接WebSocket
   */
  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.log('WebSocket already connected')
      return
    }
    
    this.isIntentionallyClosed = false
    this.createWebSocket()
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.isIntentionallyClosed = true
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }
  
  /**
   * 发送消息
   */
  send(message: WebSocketMessage | string): boolean {
    const data = typeof message === 'string' ? message : JSON.stringify(message)
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(data)
        return true
      } catch (error) {
        this.log('Send error:', error)
        this.messageQueue.push(typeof message === 'string' ? { type: 'message', data: message } : message)
        return false
      }
    } else {
      // 连接未就绪，加入队列
      this.messageQueue.push(typeof message === 'string' ? { type: 'message', data: message } : message)
      
      // 尝试重连
      if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
        this.reconnect()
      }
      
      return false
    }
  }
  
  /**
   * 订阅主题
   */
  subscribe(topics: string | string[]): void {
    const topicList = Array.isArray(topics) ? topics : [topics]
    this.send({
      type: 'subscribe',
      topics: topicList
    })
  }
  
  /**
   * 取消订阅
   */
  unsubscribe(topics: string | string[]): void {
    const topicList = Array.isArray(topics) ? topics : [topics]
    this.send({
      type: 'unsubscribe',
      topics: topicList
    })
  }
  
  /**
   * 添加事件监听器
   */
  on(event: 'message', handler: MessageHandler): void
  on(event: 'open' | 'close' | 'error' | 'reconnect', handler: EventHandler): void
  on(event: string, handler: any): void {
    if (event in this.handlers) {
      this.handlers[event as keyof typeof this.handlers].add(handler)
    }
  }
  
  /**
   * 移除事件监听器
   */
  off(event: 'message', handler: MessageHandler): void
  off(event: 'open' | 'close' | 'error' | 'reconnect', handler: EventHandler): void
  off(event: string, handler: any): void {
    if (event in this.handlers) {
      this.handlers[event as keyof typeof this.handlers].delete(handler)
    }
  }
  
  /**
   * 获取连接状态
   */
  get readyState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED
  }
  
  /**
   * 是否已连接
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }
  
  /**
   * 创建WebSocket连接
   */
  private createWebSocket(): void {
    try {
      this.ws = new WebSocket(this.config.url, this.config.protocols)
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      
      this.log('WebSocket connecting to:', this.config.url)
    } catch (error) {
      this.log('Failed to create WebSocket:', error)
      this.scheduleReconnect()
    }
  }
  
  /**
   * 处理连接打开
   */
  private handleOpen(event: Event): void {
    this.log('WebSocket connected')
    this.reconnectAttempts = 0
    
    // 发送队列中的消息
    this.flushMessageQueue()
    
    // 启动心跳
    this.startHeartbeat()
    
    // 触发open事件
    this.emit('open', event)
  }
  
  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    this.log(`WebSocket closed: ${event.code} - ${event.reason}`)
    
    this.clearTimers()
    this.ws = null
    
    // 触发close事件
    this.emit('close', event)
    
    // 自动重连
    if (!this.isIntentionallyClosed && this.config.reconnect) {
      this.scheduleReconnect()
    }
  }
  
  /**
   * 处理错误
   */
  private handleError(event: Event): void {
    this.log('WebSocket error:', event)
    this.emit('error', event)
  }
  
  /**
   * 处理消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      // 处理心跳
      if (message.type === 'ping') {
        this.send({ type: 'pong', timestamp: new Date().toISOString() })
        return
      }
      
      if (message.type === 'pong') {
        // 心跳响应
        return
      }
      
      // 触发消息事件
      this.emit('message', message)
      
    } catch (error) {
      this.log('Failed to parse message:', error)
      // 尝试作为纯文本处理
      this.emit('message', { type: 'text', data: event.data })
    }
  }
  
  /**
   * 重连
   */
  private reconnect(): void {
    if (this.isIntentionallyClosed) {
      return
    }
    
    if (this.reconnectAttempts >= this.config.reconnectMaxRetries) {
      this.log('Max reconnect attempts reached')
      return
    }
    
    this.reconnectAttempts++
    this.log(`Reconnecting... (attempt ${this.reconnectAttempts})`)
    
    this.emit('reconnect', { attempt: this.reconnectAttempts })
    this.createWebSocket()
  }
  
  /**
   * 计划重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return
    }
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(1.5, this.reconnectAttempts),
      30000 // 最大30秒
    )
    
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null
      this.reconnect()
    }, delay)
  }
  
  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping', timestamp: new Date().toISOString() })
      }
    }, this.config.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }
  
  /**
   * 清除所有定时器
   */
  private clearTimers(): void {
    this.stopHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }
  
  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }
  
  /**
   * 触发事件
   */
  private emit(event: 'message', data: WebSocketMessage): void
  private emit(event: 'open' | 'close' | 'error' | 'reconnect', data: any): void
  private emit(event: string, data: any): void {
    const handlers = this.handlers[event as keyof typeof this.handlers]
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          this.log(`Error in ${event} handler:`, error)
        }
      })
    }
  }
  
  /**
   * 日志输出
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[WebSocket]', ...args)
    }
  }
}

// 默认导出一个单例
let defaultManager: WebSocketManager | null = null

export function getWebSocketManager(config?: WebSocketConfig): WebSocketManager {
  if (!defaultManager && config) {
    defaultManager = new WebSocketManager(config)
  }
  
  if (!defaultManager) {
    throw new Error('WebSocket manager not initialized')
  }
  
  return defaultManager
}

export default WebSocketManager