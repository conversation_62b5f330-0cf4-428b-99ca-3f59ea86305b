<template>
  <div class="metrics-grid">
    <el-row :gutter="20">
      <el-col :span="6" v-for="metric in metrics" :key="metric.id">
        <component
          :is="metric.component"
          v-bind="metric.props"
          @click="handleMetricClick(metric.id)"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MetricCard from '@/components/widgets/MetricCard.vue'
import DashboardCard from '@/views/Dashboard/components/DashboardCard.vue'
import { formatCurrency } from '@/utils/format/financial'

interface Props {
  accountMetrics: {
    totalAssets: number
    dailyProfit: number
    dailyProfitPercent: number
    totalProfit: number
    totalProfitPercent: number
    positionCount: number
    activeStrategies: number
  }
  portfolioTrendData: number[]
  dailyTrendData: number[]
  totalProfitTrendData: number[]
}

interface Emits {
  (e: 'metric-click', metricId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const metrics = computed(() => [
  {
    id: 'portfolio',
    component: MetricCard,
    props: {
      title: '总资产',
      value: formatCurrency(props.accountMetrics.totalAssets || 0),
      unit: '¥',
      change: props.accountMetrics.totalProfit || 0,
      changePercent: props.accountMetrics.totalProfitPercent || 0,
      type: 'primary',
      chartData: props.portfolioTrendData,
      showChart: true,
      clickable: true
    }
  },
  {
    id: 'daily',
    component: MetricCard,
    props: {
      title: '今日盈亏',
      value: formatCurrency(Math.abs(props.accountMetrics.dailyProfit || 0)),
      unit: '¥',
      change: props.accountMetrics.dailyProfit || 0,
      changePercent: props.accountMetrics.dailyProfitPercent || 0,
      type: (props.accountMetrics.dailyProfit || 0) >= 0 ? 'success' : 'danger',
      chartData: props.dailyTrendData,
      showChart: true,
      clickable: true
    }
  },
  {
    id: 'total',
    component: MetricCard,
    props: {
      title: '总盈亏',
      value: formatCurrency(Math.abs(props.accountMetrics.totalProfit || 0)),
      unit: '¥',
      change: props.accountMetrics.totalProfit || 0,
      changePercent: props.accountMetrics.totalProfitPercent || 0,
      type: (props.accountMetrics.totalProfit || 0) >= 0 ? 'success' : 'danger',
      chartData: props.totalProfitTrendData,
      showChart: true,
      clickable: true
    }
  },
  {
    id: 'position',
    component: DashboardCard,
    props: {
      title: '持仓股票',
      value: `${props.accountMetrics.positionCount}`,
      subValue: `活跃策略: ${props.accountMetrics.activeStrategies}`,
      color: 'info',
      showProgress: true,
      progressValue: (props.accountMetrics.positionCount / 20) * 100,
      progressLabel: '仓位使用率'
    }
  }
])

const handleMetricClick = (metricId: string) => {
  emit('metric-click', metricId)
}
</script>

<style scoped>
.metrics-grid {
  margin-bottom: 24px;
}
</style>