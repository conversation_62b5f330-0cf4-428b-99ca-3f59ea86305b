<template>
  <div class="advanced-trading-chart" ref="chartContainerRef">
    <div class="chart-loading" v-if="loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>加载图表数据...</span>
    </div>
    <div 
      class="chart-main" 
      ref="chartRef"
      :style="{ height: height }"
      v-show="!loading"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { Loading } from '@element-plus/icons-vue'
import { useMarketStore } from '@/stores/modules/market'
import { useWebSocket } from '@/composables/useWebSocket'

interface Props {
  symbol: string
  period: string
  type: 'candle' | 'line'
  indicators: string[]
  drawingTool?: string
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  type: 'candle',
  period: '1m'
})

const emit = defineEmits<{
  'price-click': [price: number]
  'drawing-complete': [drawing: any]
}>()

const marketStore = useMarketStore()
const { subscribe, unsubscribe } = useWebSocket('/ws/market')

const chartContainerRef = ref<HTMLElement>()
const chartRef = ref<HTMLElement>()
const loading = ref(true)
let chart: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null

// K线数据
const klineData = ref<any[]>([])
const volumeData = ref<any[]>([])

// 技术指标计算
const calculateMA = (data: number[], period: number) => {
  const result = []
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      result.push(null)
    } else {
      let sum = 0
      for (let j = 0; j < period; j++) {
        sum += data[i - j]
      }
      result.push(sum / period)
    }
  }
  return result
}

const calculateEMA = (data: number[], period: number) => {
  const result = []
  const k = 2 / (period + 1)
  let ema = data[0]
  
  result.push(ema)
  for (let i = 1; i < data.length; i++) {
    ema = data[i] * k + ema * (1 - k)
    result.push(ema)
  }
  return result
}

const calculateMACD = (data: number[]) => {
  const ema12 = calculateEMA(data, 12)
  const ema26 = calculateEMA(data, 26)
  const dif = ema12.map((v, i) => v - ema26[i])
  const dea = calculateEMA(dif, 9)
  const macd = dif.map((v, i) => (v - dea[i]) * 2)
  
  return { dif, dea, macd }
}

const calculateRSI = (data: number[], period = 14) => {
  const result = []
  const gains = []
  const losses = []
  
  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1]
    gains.push(change > 0 ? change : 0)
    losses.push(change < 0 ? -change : 0)
  }
  
  let avgGain = gains.slice(0, period).reduce((a, b) => a + b) / period
  let avgLoss = losses.slice(0, period).reduce((a, b) => a + b) / period
  
  result.push(null)
  for (let i = 0; i < period; i++) {
    result.push(null)
  }
  
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period
    const rs = avgGain / avgLoss
    const rsi = 100 - (100 / (1 + rs))
    result.push(rsi)
  }
  
  return result
}

const calculateBOLL = (data: number[], period = 20, multiplier = 2) => {
  const ma = calculateMA(data, period)
  const upper = []
  const lower = []
  
  for (let i = 0; i < data.length; i++) {
    if (i < period - 1) {
      upper.push(null)
      lower.push(null)
    } else {
      let sum = 0
      for (let j = 0; j < period; j++) {
        sum += Math.pow(data[i - j] - ma[i], 2)
      }
      const std = Math.sqrt(sum / period)
      upper.push(ma[i] + std * multiplier)
      lower.push(ma[i] - std * multiplier)
    }
  }
  
  return { upper, middle: ma, lower }
}

// 生成图表配置
const generateChartOption = () => {
  const dates = klineData.value.map(item => item[0])
  const ohlc = klineData.value.map(item => [item[1], item[2], item[3], item[4]])
  const closes = klineData.value.map(item => item[4])
  
  const option: any = {
    animation: false,
    backgroundColor: '#0a0e17',
    grid: [],
    xAxis: [],
    yAxis: [],
    series: [],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1],
        start: 70,
        end: 100
      },
      {
        type: 'slider',
        xAxisIndex: [0, 1],
        start: 70,
        end: 100,
        height: 20,
        bottom: 0,
        borderColor: '#2a2d3a',
        textStyle: {
          color: '#9ca3af'
        },
        handleStyle: {
          color: '#3b82f6',
          borderColor: '#3b82f6'
        },
        moveHandleStyle: {
          color: '#3b82f6'
        }
      }
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#6b7280',
          width: 1,
          type: 'dashed'
        }
      },
      backgroundColor: 'rgba(15, 20, 25, 0.9)',
      borderColor: '#2a2d3a',
      textStyle: {
        color: '#e4e4e7'
      },
      formatter: (params: any) => {
        const date = params[0].name
        let html = `<div style="font-size: 12px; padding: 4px;">
          <div style="color: #9ca3af; margin-bottom: 4px;">${date}</div>`
        
        params.forEach((param: any) => {
          const value = Array.isArray(param.data) ? param.data[1] : param.data
          const color = param.color || '#e4e4e7'
          html += `<div style="display: flex; justify-content: space-between; gap: 16px;">
            <span style="color: ${color};">● ${param.seriesName}</span>
            <span style="color: #e4e4e7; font-weight: 500;">${value?.toFixed(2) || '-'}</span>
          </div>`
        })
        
        html += '</div>'
        return html
      }
    }
  }
  
  // 主图网格
  option.grid.push({
    left: 60,
    right: 60,
    top: 20,
    height: '60%'
  })
  
  // 主图X轴
  option.xAxis.push({
    type: 'category',
    data: dates,
    gridIndex: 0,
    axisLine: {
      lineStyle: {
        color: '#2a2d3a'
      }
    },
    axisLabel: {
      color: '#6b7280',
      fontSize: 11
    },
    splitLine: {
      show: false
    }
  })
  
  // 主图Y轴
  option.yAxis.push({
    type: 'value',
    gridIndex: 0,
    position: 'right',
    axisLine: {
      lineStyle: {
        color: '#2a2d3a'
      }
    },
    axisLabel: {
      color: '#6b7280',
      fontSize: 11,
      formatter: (value: number) => value.toFixed(2)
    },
    splitLine: {
      lineStyle: {
        color: '#1a1f2e'
      }
    }
  })
  
  // K线或分时线
  if (props.type === 'candle') {
    option.series.push({
      name: 'K线',
      type: 'candlestick',
      data: ohlc,
      xAxisIndex: 0,
      yAxisIndex: 0,
      itemStyle: {
        color: '#10b981',
        color0: '#ef4444',
        borderColor: '#10b981',
        borderColor0: '#ef4444'
      }
    })
  } else {
    option.series.push({
      name: '分时',
      type: 'line',
      data: closes,
      xAxisIndex: 0,
      yAxisIndex: 0,
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: '#3b82f6',
        width: 1
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
          ]
        }
      }
    })
  }
  
  // 添加指标
  let gridIndex = 1
  
  // MA指标
  if (props.indicators.includes('MA')) {
    [5, 10, 20, 60].forEach((period, index) => {
      const maData = calculateMA(closes, period)
      const colors = ['#f59e0b', '#3b82f6', '#a855f7', '#10b981']
      option.series.push({
        name: `MA${period}`,
        type: 'line',
        data: maData,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: colors[index],
          width: 1
        }
      })
    })
  }
  
  // EMA指标
  if (props.indicators.includes('EMA')) {
    [12, 26].forEach((period, index) => {
      const emaData = calculateEMA(closes, period)
      const colors = ['#ec4899', '#8b5cf6']
      option.series.push({
        name: `EMA${period}`,
        type: 'line',
        data: emaData,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: colors[index],
          width: 1
        }
      })
    })
  }
  
  // BOLL指标
  if (props.indicators.includes('BOLL')) {
    const boll = calculateBOLL(closes)
    option.series.push(
      {
        name: 'BOLL上轨',
        type: 'line',
        data: boll.upper,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#fbbf24',
          width: 1
        }
      },
      {
        name: 'BOLL中轨',
        type: 'line',
        data: boll.middle,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#60a5fa',
          width: 1
        }
      },
      {
        name: 'BOLL下轨',
        type: 'line',
        data: boll.lower,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#fbbf24',
          width: 1
        }
      }
    )
  }
  
  // 成交量
  if (props.indicators.includes('VOL')) {
    option.grid.push({
      left: 60,
      right: 60,
      height: '10%',
      top: '65%'
    })
    
    option.xAxis.push({
      type: 'category',
      data: dates,
      gridIndex: gridIndex,
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        show: false
      }
    })
    
    option.yAxis.push({
      type: 'value',
      gridIndex: gridIndex,
      position: 'right',
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
        formatter: (value: number) => {
          if (value >= 1e8) return (value / 1e8).toFixed(0) + '亿'
          if (value >= 1e4) return (value / 1e4).toFixed(0) + '万'
          return value.toFixed(0)
        }
      },
      splitLine: {
        lineStyle: {
          color: '#1a1f2e'
        }
      }
    })
    
    option.series.push({
      name: '成交量',
      type: 'bar',
      data: volumeData.value.map((v, i) => ({
        value: v,
        itemStyle: {
          color: klineData.value[i][1] <= klineData.value[i][4] ? '#10b981' : '#ef4444'
        }
      })),
      xAxisIndex: gridIndex,
      yAxisIndex: gridIndex
    })
    
    gridIndex++
  }
  
  // MACD指标
  if (props.indicators.includes('MACD')) {
    const macd = calculateMACD(closes)
    
    option.grid.push({
      left: 60,
      right: 60,
      height: '10%',
      top: gridIndex === 1 ? '65%' : '78%'
    })
    
    option.xAxis.push({
      type: 'category',
      data: dates,
      gridIndex: gridIndex,
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        show: false
      }
    })
    
    option.yAxis.push({
      type: 'value',
      gridIndex: gridIndex,
      position: 'right',
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#1a1f2e'
        }
      }
    })
    
    option.series.push(
      {
        name: 'DIF',
        type: 'line',
        data: macd.dif,
        xAxisIndex: gridIndex,
        yAxisIndex: gridIndex,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#3b82f6',
          width: 1
        }
      },
      {
        name: 'DEA',
        type: 'line',
        data: macd.dea,
        xAxisIndex: gridIndex,
        yAxisIndex: gridIndex,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#f59e0b',
          width: 1
        }
      },
      {
        name: 'MACD',
        type: 'bar',
        data: macd.macd.map(v => ({
          value: v,
          itemStyle: {
            color: v >= 0 ? '#10b981' : '#ef4444'
          }
        })),
        xAxisIndex: gridIndex,
        yAxisIndex: gridIndex
      }
    )
    
    gridIndex++
  }
  
  // RSI指标
  if (props.indicators.includes('RSI')) {
    const rsi = calculateRSI(closes)
    
    option.grid.push({
      left: 60,
      right: 60,
      height: '10%',
      top: gridIndex === 1 ? '65%' : gridIndex === 2 ? '78%' : '91%'
    })
    
    option.xAxis.push({
      type: 'category',
      data: dates,
      gridIndex: gridIndex,
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        show: false
      }
    })
    
    option.yAxis.push({
      type: 'value',
      gridIndex: gridIndex,
      position: 'right',
      min: 0,
      max: 100,
      axisLine: {
        lineStyle: {
          color: '#2a2d3a'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#1a1f2e'
        }
      }
    })
    
    option.series.push({
      name: 'RSI',
      type: 'line',
      data: rsi,
      xAxisIndex: gridIndex,
      yAxisIndex: gridIndex,
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: '#a855f7',
        width: 1
      },
      markLine: {
        silent: true,
        lineStyle: {
          color: '#6b7280',
          type: 'dashed'
        },
        data: [
          { yAxis: 30 },
          { yAxis: 70 }
        ],
        label: {
          color: '#6b7280',
          fontSize: 10
        }
      }
    })
  }
  
  return option
}

// 加载数据
const loadChartData = async () => {
  loading.value = true
  
  try {
    // 模拟数据生成
    const dataCount = 200
    const basePrice = 100
    const baseVolume = 1000000
    
    klineData.value = []
    volumeData.value = []
    
    for (let i = 0; i < dataCount; i++) {
      const date = new Date(Date.now() - (dataCount - i) * 60 * 1000)
      const dateStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      
      const open = basePrice + (Math.random() - 0.5) * 2
      const close = open + (Math.random() - 0.5) * 2
      const high = Math.max(open, close) + Math.random() * 0.5
      const low = Math.min(open, close) - Math.random() * 0.5
      const volume = baseVolume + (Math.random() - 0.5) * 500000
      
      klineData.value.push([dateStr, open, close, low, high])
      volumeData.value.push(volume)
    }
    
    await nextTick()
    
    if (chart) {
      const option = generateChartOption()
      chart.setOption(option)
    }
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value, null, {
    renderer: 'canvas'
  })
  
  // 点击事件
  chart.on('click', (params: any) => {
    if (params.componentType === 'series') {
      const price = Array.isArray(params.data) ? params.data[1] : params.data
      emit('price-click', price)
    }
  })
  
  // 画线工具
  if (props.drawingTool) {
    // 实现画线逻辑
  }
  
  await loadChartData()
}

// 订阅实时数据
const subscribeRealtimeData = () => {
  const channel = `kline.${props.symbol}.${props.period}`
  
  subscribe(channel, (data: any) => {
    // 更新最新K线
    if (klineData.value.length > 0) {
      const lastKline = klineData.value[klineData.value.length - 1]
      lastKline[2] = data.close // 更新收盘价
      lastKline[3] = Math.min(lastKline[3], data.low) // 更新最低价
      lastKline[4] = Math.max(lastKline[4], data.high) // 更新最高价
      
      volumeData.value[volumeData.value.length - 1] = data.volume
      
      if (chart && !loading.value) {
        const option = generateChartOption()
        chart.setOption(option)
      }
    }
  })
}

// 监听容器大小变化
const observeResize = () => {
  if (!chartContainerRef.value) return
  
  resizeObserver = new ResizeObserver(() => {
    chart?.resize()
  })
  
  resizeObserver.observe(chartContainerRef.value)
}

// 生命周期
onMounted(async () => {
  await nextTick()
  await initChart()
  observeResize()
  subscribeRealtimeData()
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  
  if (chart) {
    chart.dispose()
    chart = null
  }
  
  unsubscribe(`kline.${props.symbol}.${props.period}`)
})

// 监听属性变化
watch(() => props.symbol, () => {
  loadChartData()
})

watch(() => props.period, () => {
  loadChartData()
})

watch(() => props.type, () => {
  if (chart && !loading.value) {
    const option = generateChartOption()
    chart.setOption(option)
  }
})

watch(() => props.indicators, () => {
  if (chart && !loading.value) {
    const option = generateChartOption()
    chart.setOption(option)
  }
}, { deep: true })

// 暴露方法
defineExpose({
  refresh: loadChartData,
  resize: () => chart?.resize()
})
</script>

<style lang="scss" scoped>
.advanced-trading-chart {
  width: 100%;
  height: 100%;
  position: relative;
  background: #0a0e17;
  
  .chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #9ca3af;
    
    .loading-icon {
      font-size: 32px;
      animation: spin 1s linear infinite;
    }
  }
  
  .chart-main {
    width: 100%;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>