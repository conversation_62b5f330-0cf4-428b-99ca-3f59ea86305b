<template>
  <div class="trading-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>交易中心</h1>
      <div class="header-info">
        <el-tag>账户余额: ￥{{ accountBalance.toLocaleString() }}</el-tag>
        <el-tag type="success">可用资金: ￥{{ availableFunds.toLocaleString() }}</el-tag>
        <el-tag type="warning">持仓市值: ￥{{ positionValue.toLocaleString() }}</el-tag>
      </div>
    </div>

    <!-- 主要内容区 -->
    <el-row :gutter="20">
      <!-- 左侧交易面板 -->
      <el-col :xs="24" :md="8">
        <el-card class="trading-panel">
          <template #header>
            <div class="card-header">
              <span>快速交易</span>
            </div>
          </template>

          <!-- 股票选择 -->
          <el-form :model="tradeForm" label-width="80px">
            <el-form-item label="股票代码">
              <el-select
                v-model="tradeForm.symbol"
                filterable
                remote
                placeholder="请输入股票代码或名称"
                :remote-method="searchStock"
                :loading="searchLoading"
                @change="onStockSelect"
              >
                <el-option
                  v-for="stock in stockOptions"
                  :key="stock.symbol"
                  :label="`${stock.symbol} - ${stock.name}`"
                  :value="stock.symbol"
                />
              </el-select>
            </el-form-item>

            <!-- 股票信息显示 -->
            <div v-if="selectedStockInfo" class="stock-info">
              <div class="info-row">
                <span>股票名称:</span>
                <span>{{ selectedStockInfo.name }}</span>
              </div>
              <div class="info-row">
                <span>现价:</span>
                <span :class="selectedStockInfo.change > 0 ? 'price-up' : 'price-down'">
                  ￥{{ selectedStockInfo.price }}
                </span>
              </div>
              <div class="info-row">
                <span>涨跌幅:</span>
                <span :class="selectedStockInfo.change > 0 ? 'price-up' : 'price-down'">
                  {{ selectedStockInfo.change > 0 ? '+' : '' }}{{ selectedStockInfo.change }}%
                </span>
              </div>
            </div>

            <!-- 交易方向 -->
            <el-form-item label="交易方向">
              <el-radio-group v-model="tradeForm.side">
                <el-radio-button label="buy">买入</el-radio-button>
                <el-radio-button label="sell">卖出</el-radio-button>
              </el-radio-group>
            </el-form-item>

            <!-- 订单类型 -->
            <el-form-item label="订单类型">
              <el-radio-group v-model="tradeForm.orderType">
                <el-radio label="limit">限价单</el-radio>
                <el-radio label="market">市价单</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 价格（限价单） -->
            <el-form-item label="委托价格" v-if="tradeForm.orderType === 'limit'">
              <el-input-number
                v-model="tradeForm.price"
                :precision="2"
                :step="0.01"
                :min="0.01"
                style="width: 100%"
              />
            </el-form-item>

            <!-- 数量 -->
            <el-form-item label="委托数量">
              <el-input-number
                v-model="tradeForm.quantity"
                :min="100"
                :step="100"
                style="width: 100%"
              />
              <div class="quantity-shortcuts">
                <el-button size="small" @click="setQuantityRatio(0.25)">25%</el-button>
                <el-button size="small" @click="setQuantityRatio(0.5)">50%</el-button>
                <el-button size="small" @click="setQuantityRatio(0.75)">75%</el-button>
                <el-button size="small" @click="setQuantityRatio(1)">全部</el-button>
              </div>
            </el-form-item>

            <!-- 预计金额 -->
            <el-form-item label="预计金额">
              <div class="estimated-amount">
                ￥{{ estimatedAmount.toLocaleString() }}
              </div>
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="submitLoading"
                @click="submitOrder"
                style="width: 100%"
              >
                {{ tradeForm.side === 'buy' ? '买入' : '卖出' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧持仓和订单 -->
      <el-col :xs="24" :md="16">
        <!-- 标签页 -->
        <el-tabs v-model="activeTab">
          <!-- 持仓 -->
          <el-tab-pane label="当前持仓" name="positions">
            <el-table :data="positions" style="width: 100%" height="400">
              <el-table-column prop="symbol" label="代码" width="80" />
              <el-table-column prop="name" label="名称" width="100" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="avg_price" label="成本价" width="80">
                <template #default="{ row }">
                  ￥{{ row.avg_price.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="current_price" label="现价" width="80">
                <template #default="{ row }">
                  ￥{{ row.current_price.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="profit" label="盈亏" width="100">
                <template #default="{ row }">
                  <span :class="row.profit > 0 ? 'profit-up' : 'profit-down'">
                    {{ row.profit > 0 ? '+' : '' }}￥{{ row.profit.toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="profit_rate" label="盈亏率" width="80">
                <template #default="{ row }">
                  <span :class="row.profit_rate > 0 ? 'profit-up' : 'profit-down'">
                    {{ row.profit_rate > 0 ? '+' : '' }}{{ row.profit_rate.toFixed(2) }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button size="small" @click="quickSell(row)">卖出</el-button>
                  <el-button size="small" type="primary" @click="addPosition(row)">加仓</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 当日委托 -->
          <el-tab-pane label="当日委托" name="orders">
            <el-table :data="orders" style="width: 100%" height="400">
              <el-table-column prop="id" label="订单号" width="80" />
              <el-table-column prop="symbol" label="代码" width="80" />
              <el-table-column prop="side" label="方向" width="60">
                <template #default="{ row }">
                  <el-tag :type="row.side === 'buy' ? 'success' : 'danger'" size="small">
                    {{ row.side === 'buy' ? '买入' : '卖出' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="price" label="价格" width="80">
                <template #default="{ row }">
                  ￥{{ row.price?.toFixed(2) || '市价' }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getOrderStatusType(row.status)" size="small">
                    {{ getOrderStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="时间" width="150" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button
                    v-if="row.status === 'pending'"
                    size="small"
                    type="danger"
                    @click="cancelOrder(row)"
                  >
                    撤单
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <!-- 历史成交 -->
          <el-tab-pane label="历史成交" name="history">
            <el-table :data="historyOrders" style="width: 100%" height="400">
              <el-table-column prop="id" label="订单号" width="80" />
              <el-table-column prop="symbol" label="代码" width="80" />
              <el-table-column prop="side" label="方向" width="60">
                <template #default="{ row }">
                  <el-tag :type="row.side === 'buy' ? 'success' : 'danger'" size="small">
                    {{ row.side === 'buy' ? '买入' : '卖出' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="price" label="成交价" width="80">
                <template #default="{ row }">
                  ￥{{ row.price.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="成交额" width="100">
                <template #default="{ row }">
                  ￥{{ (row.price * row.quantity).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="fee" label="手续费" width="80">
                <template #default="{ row }">
                  ￥{{ row.fee?.toFixed(2) || '0.00' }}
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="成交时间" width="150" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { optimizedAPI } from '@/api/optimized-api'

// 状态
const activeTab = ref('positions')
const searchLoading = ref(false)
const submitLoading = ref(false)

// 账户信息
const accountBalance = ref(1000000)
const availableFunds = ref(500000)
const positionValue = ref(500000)

// 数据
const positions = ref<any[]>([])
const orders = ref<any[]>([])
const historyOrders = ref<any[]>([])
const stockOptions = ref<any[]>([])
const selectedStockInfo = ref<any>(null)

// 交易表单
const tradeForm = reactive({
  symbol: '',
  side: 'buy',
  orderType: 'limit',
  price: 0,
  quantity: 100,
})

// 计算预计金额
const estimatedAmount = computed(() => {
  if (tradeForm.orderType === 'market' && selectedStockInfo.value) {
    return selectedStockInfo.value.price * tradeForm.quantity
  }
  return tradeForm.price * tradeForm.quantity
})

// 搜索股票
const searchStock = async (query: string) => {
  if (query) {
    searchLoading.value = true
    try {
      const response = await optimizedAPI.market.getStockList()
      stockOptions.value = response.data.filter((stock: any) =>
        stock.symbol.includes(query) || stock.name.includes(query)
      )
    } catch (error) {
      console.error('搜索股票失败:', error)
    } finally {
      searchLoading.value = false
    }
  }
}

// 选择股票
const onStockSelect = async (symbol: string) => {
  try {
    const response = await optimizedAPI.market.getRealtimeData(symbol)
    selectedStockInfo.value = response
    tradeForm.price = response.price
  } catch (error) {
    console.error('获取股票信息失败:', error)
  }
}

// 设置数量比例
const setQuantityRatio = (ratio: number) => {
  if (tradeForm.side === 'buy') {
    // 买入时根据可用资金计算
    const maxQuantity = Math.floor(availableFunds.value / (tradeForm.price || selectedStockInfo.value?.price || 1) / 100) * 100
    tradeForm.quantity = Math.floor(maxQuantity * ratio / 100) * 100
  } else {
    // 卖出时根据持仓数量计算
    const position = positions.value.find(p => p.symbol === tradeForm.symbol)
    if (position) {
      tradeForm.quantity = Math.floor(position.quantity * ratio / 100) * 100
    }
  }
}

// 提交订单
const submitOrder = async () => {
  if (!tradeForm.symbol) {
    ElMessage.warning('请选择股票')
    return
  }

  if (tradeForm.quantity <= 0) {
    ElMessage.warning('请输入正确的数量')
    return
  }

  if (tradeForm.orderType === 'limit' && tradeForm.price <= 0) {
    ElMessage.warning('请输入正确的价格')
    return
  }

  submitLoading.value = true
  try {
    const order = {
      symbol: tradeForm.symbol,
      side: tradeForm.side as 'buy' | 'sell',
      quantity: tradeForm.quantity,
      price: tradeForm.orderType === 'limit' ? tradeForm.price : undefined,
      order_type: tradeForm.orderType as 'limit' | 'market'
    }

    const response = await optimizedAPI.trading.createOrder(order)
    
    ElNotification({
      title: '下单成功',
      message: `${tradeForm.side === 'buy' ? '买入' : '卖出'}委托已提交`,
      type: 'success'
    })

    // 刷新订单列表
    await fetchOrders()

    // 重置表单
    tradeForm.quantity = 100
    if (tradeForm.orderType === 'limit') {
      tradeForm.price = selectedStockInfo.value?.price || 0
    }
  } catch (error) {
    console.error('下单失败:', error)
    ElMessage.error('下单失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

// 快速卖出
const quickSell = (position: any) => {
  tradeForm.symbol = position.symbol
  tradeForm.side = 'sell'
  tradeForm.quantity = position.quantity
  tradeForm.price = position.current_price
  selectedStockInfo.value = {
    symbol: position.symbol,
    name: position.name,
    price: position.current_price,
    change: 0
  }
  ElMessage.info('已填充卖出信息，请确认后提交')
}

// 加仓
const addPosition = (position: any) => {
  tradeForm.symbol = position.symbol
  tradeForm.side = 'buy'
  tradeForm.quantity = 100
  tradeForm.price = position.current_price
  selectedStockInfo.value = {
    symbol: position.symbol,
    name: position.name,
    price: position.current_price,
    change: 0
  }
  ElMessage.info('已填充加仓信息，请确认后提交')
}

// 撤单
const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确定要撤销该委托吗？', '撤单确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用撤单API
    await optimizedAPI.trading.cancelOrder(order.id)
    
    ElMessage.success('撤单成功')
    
    // 刷新订单列表
    await fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤单失败:', error)
      ElMessage.error('撤单失败')
    }
  }
}

// 获取订单状态类型
const getOrderStatusType = (status: string) => {
  const typeMap: any = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const textMap: any = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return textMap[status] || status
}

// 获取持仓列表
const fetchPositions = async () => {
  try {
    const response = await optimizedAPI.trading.getPositions()
    positions.value = response.data || []
    
    // 计算持仓市值
    positionValue.value = positions.value.reduce((sum, p) => sum + p.current_price * p.quantity, 0)
  } catch (error) {
    console.error('获取持仓失败:', error)
  }
}

// 获取订单列表
const fetchOrders = async () => {
  try {
    const response = await optimizedAPI.trading.getOrders()
    const allOrders = response.data || []
    
    // 分离当日委托和历史成交
    orders.value = allOrders.filter((o: any) => o.status === 'pending')
    historyOrders.value = allOrders.filter((o: any) => o.status === 'filled')
  } catch (error) {
    console.error('获取订单失败:', error)
  }
}

// 初始化
onMounted(() => {
  fetchPositions()
  fetchOrders()
  
  // 获取初始股票列表
  optimizedAPI.market.getStockList().then(response => {
    stockOptions.value = response.data || []
  })
})
</script>

<style scoped lang="scss">
.trading-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
  }

  .header-info {
    display: flex;
    gap: 10px;
  }
}

.trading-panel {
  .stock-info {
    background: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;

    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .quantity-shortcuts {
    display: flex;
    gap: 5px;
    margin-top: 10px;
  }

  .estimated-amount {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }
}

.price-up {
  color: #f56c6c;
}

.price-down {
  color: #67c23a;
}

.profit-up {
  color: #f56c6c;
  font-weight: bold;
}

.profit-down {
  color: #67c23a;
  font-weight: bold;
}
</style>