/**
 * Fixed main entry point - resolves undefined errors
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'

// Import App component safely
import App from './App.vue'

// Simple router configuration
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL || '/'),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: () => import('./views/Dashboard/DashboardView.vue').catch(() => ({
        template: `
          <div style="padding: 20px;">
            <h1>仪表盘</h1>
            <p>页面加载中...</p>
          </div>
        `
      }))
    },
    {
      path: '/market',
      name: 'market',
      component: () => import('./views/Market/MarketView.vue').catch(() => ({
        template: `
          <div style="padding: 20px;">
            <h1>市场行情</h1>
            <p>页面加载中...</p>
          </div>
        `
      }))
    },
    {
      path: '/trading',
      name: 'trading',
      component: () => import('./views/Trading/TradingView.vue').catch(() => ({
        template: `
          <div style="padding: 20px;">
            <h1>交易中心</h1>
            <p>页面加载中...</p>
          </div>
        `
      }))
    },
    {
      path: '/strategy',
      name: 'strategy',
      component: () => import('./views/Strategy/StrategyView.vue').catch(() => ({
        template: `
          <div style="padding: 20px;">
            <h1>策略中心</h1>
            <p>页面加载中...</p>
          </div>
        `
      }))
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: {
        template: `
          <div style="padding: 40px; text-align: center;">
            <h1>404 - 页面未找到</h1>
            <p>请检查您的URL是否正确</p>
            <a href="/" style="color: #409EFF;">返回首页</a>
          </div>
        `
      }
    }
  ]
})

async function initApp() {
  try {
    console.log('🚀 Initializing Quantum Investment Platform...')

    // Create Vue app
    const app = createApp(App)
    
    // Create Pinia store
    const pinia = createPinia()
    
    // Install plugins
    app.use(pinia)
    app.use(router)
    app.use(ElementPlus, {
      locale: zhCn,
    })
    
    // Register Element Plus icons
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    
    // Global error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('Vue Error:', err)
      console.error('Component:', instance)
      console.error('Info:', info)
      
      // Show user-friendly error message
      if (typeof ElMessage !== 'undefined') {
        ElMessage.error('系统错误，请刷新页面重试')
      }
    }
    
    // Configure global properties
    app.config.globalProperties.$apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
    
    // Mount app
    const mountedApp = app.mount('#app')
    
    console.log('✅ Application initialized successfully')
    
    // Export for debugging in development
    if (import.meta.env.DEV) {
      (window as any).__VUE_APP__ = app
      (window as any).__VUE_ROUTER__ = router
      (window as any).__PINIA__ = pinia
    }
    
    return mountedApp
    
  } catch (error) {
    console.error('❌ Application initialization failed:', error)
    
    // Display error in UI
    const appElement = document.getElementById('app')
    if (appElement) {
      appElement.innerHTML = `
        <div style="padding: 40px; font-family: system-ui, -apple-system, sans-serif;">
          <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #f56c6c; margin-bottom: 20px;">❌ 应用加载失败</h1>
            <div style="background: #fef0f0; border-left: 4px solid #f56c6c; padding: 15px; margin-bottom: 20px;">
              <p style="margin: 0; color: #666;">错误信息：${error.message}</p>
            </div>
            <details style="margin-bottom: 20px;">
              <summary style="cursor: pointer; color: #409EFF;">查看详细错误</summary>
              <pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; margin-top: 10px;">${error.stack}</pre>
            </details>
            <div style="display: flex; gap: 10px;">
              <button onclick="location.reload()" style="padding: 10px 20px; background: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">
                刷新页面
              </button>
              <button onclick="localStorage.clear(); location.reload()" style="padding: 10px 20px; background: #67c23a; color: white; border: none; border-radius: 4px; cursor: pointer;">
                清除缓存并刷新
              </button>
            </div>
          </div>
        </div>
      `
    }
    
    throw error
  }
}

// Initialize app
initApp().catch(console.error)

// HMR support
if (import.meta.hot) {
  import.meta.hot.accept(() => {
    console.log('HMR update received')
  })
}