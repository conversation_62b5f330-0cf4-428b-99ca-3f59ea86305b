<template>
  <div class="connection-status" :class="`status-${status}`">
    <div class="status-indicator">
      <div class="status-dot"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>
    <div v-if="quality && isConnected" class="connection-quality">
      <span :class="`quality-${quality}`">{{ qualityText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  status: 'connected' | 'connecting' | 'disconnected' | 'error'
  quality?: 'excellent' | 'good' | 'poor'
}

const props = defineProps<Props>()

const isConnected = computed(() => props.status === 'connected')

const statusText = computed(() => {
  const statusMap = {
    connected: '实时连接',
    connecting: '连接中...',
    error: '连接错误',
    disconnected: '已断开'
  }
  return statusMap[props.status]
})

const qualityText = computed(() => {
  const qualityMap = {
    excellent: '优秀',
    good: '良好',
    poor: '较差'
  }
  return props.quality ? qualityMap[props.quality] : ''
})
</script>

<style scoped>
.connection-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-connected .status-dot {
  background: #67c23a;
}

.status-connecting .status-dot {
  background: #e6a23c;
}

.status-disconnected .status-dot {
  background: #909399;
}

.status-error .status-dot {
  background: #f56c6c;
}

.status-text {
  color: #666;
  font-weight: 500;
}

.connection-quality {
  font-size: 10px;
}

.quality-excellent {
  color: #67c23a;
}

.quality-good {
  color: #e6a23c;
}

.quality-poor {
  color: #f56c6c;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>