import { ElMessage, ElNotification } from 'element-plus'
import { Router } from 'vue-router'

export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export enum ErrorType {
  NETWORK = 'network',
  API = 'api',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  BUSINESS = 'business',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

export interface ErrorInfo {
  code?: string
  message: string
  type: ErrorType
  level: ErrorLevel
  timestamp: Date
  details?: any
  stack?: string
  url?: string
  method?: string
  params?: any
}

export interface ErrorHandlerConfig {
  enableLogging: boolean
  enableNotification: boolean
  enableRedirect: boolean
  logToServer: boolean
  serverLogUrl?: string
  maxRetries: number
  retryDelay: number
}

class ErrorHandler {
  private config: ErrorHandlerConfig
  private errorHistory: ErrorInfo[] = []
  private maxHistorySize = 100
  private router: Router | null = null
  private retryMap = new Map<string, number>()

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableLogging: true,
      enableNotification: true,
      enableRedirect: true,
      logToServer: false,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    }
  }

  // 初始化路由
  setRouter(router: Router): void {
    this.router = router
  }

  // 处理错误
  handle(error: Error | ErrorInfo | any, context?: any): void {
    const errorInfo = this.normalizeError(error, context)
    
    // 添加到历史记录
    this.addToHistory(errorInfo)
    
    // 根据错误级别处理
    switch (errorInfo.level) {
      case ErrorLevel.CRITICAL:
        this.handleCriticalError(errorInfo)
        break
      case ErrorLevel.ERROR:
        this.handleError(errorInfo)
        break
      case ErrorLevel.WARNING:
        this.handleWarning(errorInfo)
        break
      case ErrorLevel.INFO:
        this.handleInfo(errorInfo)
        break
    }
    
    // 日志记录
    if (this.config.enableLogging) {
      this.logError(errorInfo)
    }
    
    // 发送到服务器
    if (this.config.logToServer && this.config.serverLogUrl) {
      this.sendToServer(errorInfo)
    }
  }

  // 处理API错误
  handleApiError(error: any, config?: any): Promise<any> {
    const errorInfo = this.parseApiError(error, config)
    
    // 检查是否需要重试
    if (this.shouldRetry(errorInfo, config)) {
      return this.retryRequest(config)
    }
    
    // 特殊错误处理
    switch (errorInfo.code) {
      case '401':
        this.handleUnauthorized()
        break
      case '403':
        this.handleForbidden()
        break
      case '404':
        this.handleNotFound(errorInfo)
        break
      case '500':
      case '502':
      case '503':
        this.handleServerError(errorInfo)
        break
      default:
        this.handle(errorInfo)
    }
    
    return Promise.reject(error)
  }

  // 处理WebSocket错误
  handleWebSocketError(error: any): void {
    const errorInfo: ErrorInfo = {
      message: '实时连接出现问题',
      type: ErrorType.NETWORK,
      level: ErrorLevel.WARNING,
      timestamp: new Date(),
      details: error
    }
    
    this.handle(errorInfo)
    
    // 显示重连提示
    ElNotification({
      title: '连接提示',
      message: '正在尝试重新连接到服务器...',
      type: 'warning',
      duration: 3000
    })
  }

  // 规范化错误信息
  private normalizeError(error: any, context?: any): ErrorInfo {
    if (this.isErrorInfo(error)) {
      return error
    }
    
    const errorInfo: ErrorInfo = {
      message: this.extractMessage(error),
      type: this.detectErrorType(error),
      level: this.detectErrorLevel(error),
      timestamp: new Date(),
      details: context
    }
    
    if (error instanceof Error) {
      errorInfo.stack = error.stack
    }
    
    if (error.code) {
      errorInfo.code = String(error.code)
    }
    
    return errorInfo
  }

  // 解析API错误
  private parseApiError(error: any, config?: any): ErrorInfo {
    const errorInfo: ErrorInfo = {
      message: '',
      type: ErrorType.API,
      level: ErrorLevel.ERROR,
      timestamp: new Date(),
      url: config?.url,
      method: config?.method,
      params: config?.params || config?.data
    }
    
    if (error.response) {
      // 服务器响应错误
      errorInfo.code = String(error.response.status)
      errorInfo.message = error.response.data?.message || 
                         error.response.statusText || 
                         '服务器响应错误'
      errorInfo.details = error.response.data
    } else if (error.request) {
      // 请求未收到响应
      errorInfo.type = ErrorType.NETWORK
      errorInfo.message = '网络连接失败，请检查网络设置'
      errorInfo.level = ErrorLevel.WARNING
    } else {
      // 请求配置错误
      errorInfo.message = error.message || '请求配置错误'
      errorInfo.level = ErrorLevel.ERROR
    }
    
    return errorInfo
  }

  // 检测错误类型
  private detectErrorType(error: any): ErrorType {
    if (error.type) return error.type
    
    const message = String(error.message || '').toLowerCase()
    
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return ErrorType.PERMISSION
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ErrorType.VALIDATION
    }
    if (error.response) {
      return ErrorType.API
    }
    
    return ErrorType.UNKNOWN
  }

  // 检测错误级别
  private detectErrorLevel(error: any): ErrorLevel {
    if (error.level) return error.level
    
    const code = error.code || error.response?.status
    
    if (code >= 500) return ErrorLevel.CRITICAL
    if (code >= 400) return ErrorLevel.ERROR
    if (code >= 300) return ErrorLevel.WARNING
    
    return ErrorLevel.ERROR
  }

  // 提取错误消息
  private extractMessage(error: any): string {
    if (typeof error === 'string') return error
    if (error.message) return error.message
    if (error.msg) return error.msg
    if (error.error) return this.extractMessage(error.error)
    if (error.response?.data?.message) return error.response.data.message
    
    return '未知错误'
  }

  // 处理不同级别的错误
  private handleCriticalError(errorInfo: ErrorInfo): void {
    console.error('[CRITICAL]', errorInfo)
    
    if (this.config.enableNotification) {
      ElNotification({
        title: '系统错误',
        message: errorInfo.message,
        type: 'error',
        duration: 0,
        showClose: true
      })
    }
    
    // 跳转到错误页面
    if (this.config.enableRedirect && this.router) {
      this.router.push('/error/500')
    }
  }

  private handleError(errorInfo: ErrorInfo): void {
    console.error('[ERROR]', errorInfo)
    
    if (this.config.enableNotification) {
      ElMessage.error({
        message: errorInfo.message,
        duration: 5000,
        showClose: true
      })
    }
  }

  private handleWarning(errorInfo: ErrorInfo): void {
    console.warn('[WARNING]', errorInfo)
    
    if (this.config.enableNotification) {
      ElMessage.warning({
        message: errorInfo.message,
        duration: 3000
      })
    }
  }

  private handleInfo(errorInfo: ErrorInfo): void {
    console.info('[INFO]', errorInfo)
    
    if (this.config.enableNotification) {
      ElMessage.info({
        message: errorInfo.message,
        duration: 2000
      })
    }
  }

  // 特殊错误处理
  private handleUnauthorized(): void {
    ElMessage.error('登录已过期，请重新登录')
    
    if (this.router) {
      // 清除用户信息
      localStorage.removeItem('token')
      sessionStorage.clear()
      
      // 跳转到登录页
      this.router.push('/login')
    }
  }

  private handleForbidden(): void {
    ElMessage.error('您没有权限访问该资源')
    
    if (this.router) {
      this.router.push('/error/403')
    }
  }

  private handleNotFound(errorInfo: ErrorInfo): void {
    ElMessage.error(errorInfo.message || '请求的资源不存在')
  }

  private handleServerError(errorInfo: ErrorInfo): void {
    ElNotification({
      title: '服务器错误',
      message: '服务器暂时无法处理请求，请稍后重试',
      type: 'error',
      duration: 5000
    })
  }

  // 重试机制
  private shouldRetry(errorInfo: ErrorInfo, config: any): boolean {
    if (!config || !config.retry) return false
    
    const key = `${config.method}-${config.url}`
    const retryCount = this.retryMap.get(key) || 0
    
    if (retryCount >= this.config.maxRetries) {
      this.retryMap.delete(key)
      return false
    }
    
    // 只重试网络错误和5xx错误
    const isRetryable = errorInfo.type === ErrorType.NETWORK || 
                       (errorInfo.code && errorInfo.code.startsWith('5'))
    
    if (isRetryable) {
      this.retryMap.set(key, retryCount + 1)
      return true
    }
    
    return false
  }

  private async retryRequest(config: any): Promise<any> {
    const key = `${config.method}-${config.url}`
    const retryCount = this.retryMap.get(key) || 1
    
    console.log(`Retrying request (${retryCount}/${this.config.maxRetries}):`, config.url)
    
    // 延迟重试
    await new Promise(resolve => {
      setTimeout(resolve, this.config.retryDelay * retryCount)
    })
    
    // 重新发起请求
    return config.retry()
  }

  // 日志记录
  private logError(errorInfo: ErrorInfo): void {
    const logEntry = {
      ...errorInfo,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: errorInfo.timestamp.toISOString()
    }
    
    console.error('Error logged:', logEntry)
  }

  // 发送到服务器
  private async sendToServer(errorInfo: ErrorInfo): Promise<void> {
    if (!this.config.serverLogUrl) return
    
    try {
      await fetch(this.config.serverLogUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...errorInfo,
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })
    } catch (error) {
      console.error('Failed to send error to server:', error)
    }
  }

  // 添加到历史记录
  private addToHistory(errorInfo: ErrorInfo): void {
    this.errorHistory.unshift(errorInfo)
    
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.pop()
    }
  }

  // 工具方法
  private isErrorInfo(obj: any): obj is ErrorInfo {
    return obj && 
           typeof obj.message === 'string' && 
           obj.type && 
           obj.level && 
           obj.timestamp instanceof Date
  }

  // 获取错误历史
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorHistory]
  }

  // 清除错误历史
  clearErrorHistory(): void {
    this.errorHistory = []
  }

  // 获取错误统计
  getErrorStats(): Record<ErrorType, number> {
    const stats: Record<ErrorType, number> = {
      [ErrorType.NETWORK]: 0,
      [ErrorType.API]: 0,
      [ErrorType.VALIDATION]: 0,
      [ErrorType.PERMISSION]: 0,
      [ErrorType.BUSINESS]: 0,
      [ErrorType.SYSTEM]: 0,
      [ErrorType.UNKNOWN]: 0
    }
    
    this.errorHistory.forEach(error => {
      stats[error.type]++
    })
    
    return stats
  }
}

// 创建全局实例
export const errorHandler = new ErrorHandler()

// Vue插件安装
export default {
  install(app: any, options?: Partial<ErrorHandlerConfig>) {
    const handler = new ErrorHandler(options)
    
    // 全局错误处理
    app.config.errorHandler = (error: Error, instance: any, info: string) => {
      handler.handle(error, { component: instance, info })
    }
    
    // 全局属性
    app.config.globalProperties.$errorHandler = handler
    
    // 提供注入
    app.provide('errorHandler', handler)
  }
}