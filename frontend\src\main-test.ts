/**
 * 最简单的测试入口文件
 */

console.log('🚀 开始加载应用...')

// 直接操作DOM，不依赖Vue
const app = document.getElementById('app')
if (app) {
  app.innerHTML = `
    <div style="
      min-height: 100vh; 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div style="
        background: white;
        padding: 3rem;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        max-width: 600px;
        text-align: center;
      ">
        <h1 style="color: #2c3e50; margin-bottom: 1rem; font-size: 2.5rem;">
          🚀 量化投资平台
        </h1>
        <p style="color: #6b7280; margin-bottom: 2rem; font-size: 1.1rem;">
          前端应用已成功加载！
        </p>
        
        <div style="
          display: grid; 
          grid-template-columns: repeat(2, 1fr); 
          gap: 1rem; 
          margin-bottom: 2rem;
        ">
          <div style="
            padding: 1rem; 
            background: #f8fafc; 
            border-radius: 8px; 
            border-left: 4px solid #3b82f6;
          ">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📊 仪表盘</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">投资数据概览</p>
          </div>
          
          <div style="
            padding: 1rem; 
            background: #f8fafc; 
            border-radius: 8px; 
            border-left: 4px solid #10b981;
          ">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📈 市场行情</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">实时市场数据</p>
          </div>
          
          <div style="
            padding: 1rem; 
            background: #f8fafc; 
            border-radius: 8px; 
            border-left: 4px solid #f59e0b;
          ">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">💰 智能交易</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">自动化交易系统</p>
          </div>
          
          <div style="
            padding: 1rem; 
            background: #f8fafc; 
            border-radius: 8px; 
            border-left: 4px solid #8b5cf6;
          ">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">🧠 策略研发</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">量化策略开发</p>
          </div>
        </div>

        <div style="
          background: #e8f5e8; 
          padding: 1rem; 
          border-radius: 8px; 
          border-left: 4px solid #10b981;
          margin-bottom: 2rem;
        ">
          <p style="color: #065f46; font-weight: 500;">
            ✅ 前端应用正常运行！
          </p>
          <p style="color: #047857; font-size: 0.9rem;">
            基础页面加载成功，可以继续开发完整功能
          </p>
        </div>

        <div style="
          display: flex;
          gap: 1rem;
          justify-content: center;
        ">
          <button onclick="testAPI()" style="
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
          ">
            测试API连接
          </button>
          
          <button onclick="showInfo()" style="
            padding: 0.75rem 1.5rem;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
          ">
            系统信息
          </button>
        </div>

        <div id="test-result" style="margin-top: 2rem;"></div>
      </div>
    </div>
  `

  console.log('✅ 页面内容已加载')
} else {
  console.error('❌ 找不到 #app 元素')
}

// 添加测试函数
(window as any).testAPI = async function() {
  const resultDiv = document.getElementById('test-result')
  if (!resultDiv) return

  resultDiv.innerHTML = '<p style="color: #6b7280;">正在测试API连接...</p>'

  try {
    const response = await fetch('http://127.0.0.1:8000/api/v1/health')
    const data = await response.json()
    
    resultDiv.innerHTML = `
      <div style="
        background: #e8f5e8; 
        padding: 1rem; 
        border-radius: 8px; 
        border-left: 4px solid #10b981;
      ">
        <p style="color: #065f46; font-weight: 500;">✅ API连接成功！</p>
        <p style="color: #047857; font-size: 0.9rem;">
          状态: ${response.status} | 响应: ${JSON.stringify(data)}
        </p>
      </div>
    `
  } catch (error) {
    resultDiv.innerHTML = `
      <div style="
        background: #fee; 
        padding: 1rem; 
        border-radius: 8px; 
        border-left: 4px solid #f56c6c;
      ">
        <p style="color: #c53030; font-weight: 500;">❌ API连接失败</p>
        <p style="color: #e53e3e; font-size: 0.9rem;">
          错误: ${error.message}
        </p>
      </div>
    `
  }
}

// 添加系统信息函数
(window as any).showInfo = function() {
  const resultDiv = document.getElementById('test-result')
  if (!resultDiv) return

  resultDiv.innerHTML = `
    <div style="
      background: #f0f9ff; 
      padding: 1rem; 
      border-radius: 8px; 
      border-left: 4px solid #3b82f6;
      text-align: left;
    ">
      <h4 style="color: #1e40af; margin-bottom: 0.5rem;">系统信息</h4>
      <p style="color: #1d4ed8; font-size: 0.9rem; margin: 0.25rem 0;">
        <strong>前端地址:</strong> ${window.location.href}
      </p>
      <p style="color: #1d4ed8; font-size: 0.9rem; margin: 0.25rem 0;">
        <strong>后端地址:</strong> http://127.0.0.1:8000
      </p>
      <p style="color: #1d4ed8; font-size: 0.9rem; margin: 0.25rem 0;">
        <strong>用户代理:</strong> ${navigator.userAgent.substring(0, 50)}...
      </p>
      <p style="color: #1d4ed8; font-size: 0.9rem; margin: 0.25rem 0;">
        <strong>时间:</strong> ${new Date().toLocaleString()}
      </p>
    </div>
  `
}

console.log('✅ 应用初始化完成')
console.log('🌐 前端地址:', window.location.href)
console.log('🔙 后端地址: http://127.0.0.1:8000')
