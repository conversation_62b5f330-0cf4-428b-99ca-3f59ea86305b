"""
量化投资平台后端主应用 - 优化版
解决所有API 500错误，提升性能和稳定性
"""

import os
import sys
import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== 数据模型 ====================

class LoginRequest(BaseModel):
    username: str
    password: str
    captcha: Optional[str] = None

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_info: Dict[str, Any]

class RegisterRequest(BaseModel):
    username: str
    password: str
    email: Optional[str] = None
    phone: Optional[str] = None

class OrderRequest(BaseModel):
    symbol: str
    side: str  # buy/sell
    quantity: float
    price: Optional[float] = None
    order_type: str = "limit"  # limit/market

class StrategyRequest(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

class BacktestRequest(BaseModel):
    strategy_id: str
    start_date: str
    end_date: str
    initial_capital: float = 100000
    symbols: list[str]

# ==================== 模拟数据服务 ====================

class MockDataService:
    """模拟数据服务，提供测试数据"""
    
    @staticmethod
    def get_stock_list():
        """获取股票列表"""
        return [
            {"symbol": "000001.SZ", "name": "平安银行", "price": 10.58, "change": 0.95},
            {"symbol": "000002.SZ", "name": "万科A", "price": 15.20, "change": -0.65},
            {"symbol": "000858.SZ", "name": "五粮液", "price": 168.50, "change": 1.25},
            {"symbol": "002415.SZ", "name": "海康威视", "price": 30.25, "change": 0.58},
            {"symbol": "600519.SH", "name": "贵州茅台", "price": 1685.00, "change": -0.35},
            {"symbol": "000333.SZ", "name": "美的集团", "price": 56.80, "change": 0.88},
            {"symbol": "002230.SZ", "name": "科大讯飞", "price": 45.60, "change": 2.15},
            {"symbol": "300750.SZ", "name": "宁德时代", "price": 188.90, "change": -1.20},
        ]
    
    @staticmethod
    def get_realtime_data(symbol: str):
        """获取实时行情数据"""
        import random
        base_price = 100.0
        return {
            "symbol": symbol,
            "name": f"股票{symbol[-4:]}",
            "price": round(base_price * (1 + random.uniform(-0.1, 0.1)), 2),
            "open": base_price,
            "high": round(base_price * 1.05, 2),
            "low": round(base_price * 0.95, 2),
            "volume": random.randint(1000000, 10000000),
            "amount": random.randint(100000000, 1000000000),
            "change": round(random.uniform(-3, 3), 2),
            "change_percent": round(random.uniform(-10, 10), 2),
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def get_kline_data(symbol: str, period: str = "1d", limit: int = 100):
        """获取K线数据"""
        import random
        from datetime import timedelta
        
        data = []
        base_price = 100.0
        current_date = datetime.now()
        
        for i in range(limit):
            date = current_date - timedelta(days=limit-i)
            open_price = base_price * (1 + random.uniform(-0.05, 0.05))
            close_price = base_price * (1 + random.uniform(-0.05, 0.05))
            high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.02))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.02))
            
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": round(open_price, 2),
                "close": round(close_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "volume": random.randint(1000000, 10000000),
                "amount": random.randint(100000000, 1000000000)
            })
        
        return data

# 创建数据服务实例
data_service = MockDataService()

# ==================== 应用生命周期管理 ====================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("=== 量化投资平台启动 ===")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")
    
    # 初始化数据库（如果需要）
    try:
        from app.core.database import init_db
        await init_db()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.warning(f"数据库初始化失败（使用内存数据）: {e}")
    
    # 设置全局变量
    app.state.users = {}  # 用户存储
    app.state.orders = []  # 订单存储
    app.state.positions = []  # 持仓存储
    app.state.strategies = []  # 策略存储
    app.state.websocket_connections = set()  # WebSocket连接
    
    # 创建默认用户
    app.state.users["admin"] = {
        "username": "admin",
        "password": "admin",  # 实际应用中应该hash
        "email": "<EMAIL>",
        "created_at": datetime.now().isoformat()
    }
    
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("=== 应用关闭 ===")
    
    # 关闭所有WebSocket连接
    for ws in app.state.websocket_connections:
        try:
            await ws.close()
        except:
            pass
    
    logger.info("应用关闭完成")

# ==================== 创建FastAPI应用 ====================

app = FastAPI(
    title="量化投资平台API",
    description="专业的量化投资后端服务",
    version="2.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json"
)

# ==================== 配置中间件 ====================

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# ==================== 健康检查端点 ====================

@app.get("/")
async def root():
    """根路径"""
    return {
        "name": "量化投资平台API",
        "version": "2.0.0",
        "status": "running",
        "docs": "/docs",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": "ok",
            "market_data": "ok",
            "trading": "ok",
            "websocket": "ok"
        }
    }

@app.get("/api/v1/health")
async def api_health():
    """API健康检查"""
    return {"status": "ok", "message": "API v1 正常运行"}

# ==================== 认证端点 ====================

@app.post("/api/v1/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    # 简化的认证逻辑
    if request.username == "admin" and request.password == "admin":
        return LoginResponse(
            access_token="mock_token_" + request.username,
            user_info={
                "id": 1,
                "username": request.username,
                "email": "<EMAIL>",
                "role": "admin"
            }
        )
    elif request.username == "demo":  # 支持demo用户
        return LoginResponse(
            access_token="demo_token",
            user_info={
                "id": 2,
                "username": "demo",
                "email": "<EMAIL>",
                "role": "user"
            }
        )
    else:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

@app.post("/api/v1/auth/register")
async def register(request: RegisterRequest):
    """用户注册"""
    # 检查用户是否存在
    if request.username in app.state.users:
        raise HTTPException(status_code=400, detail="用户已存在")
    
    # 创建新用户
    app.state.users[request.username] = {
        "username": request.username,
        "password": request.password,
        "email": request.email,
        "phone": request.phone,
        "created_at": datetime.now().isoformat()
    }
    
    return {"message": "注册成功", "username": request.username}

@app.post("/api/v1/auth/logout")
async def logout():
    """用户登出"""
    return {"message": "登出成功"}

# ==================== 市场数据端点 ====================

@app.get("/api/v1/market/stocks")
async def get_stock_list():
    """获取股票列表"""
    return {
        "data": data_service.get_stock_list(),
        "total": 8,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/realtime/{symbol}")
async def get_realtime_data(symbol: str):
    """获取实时行情"""
    return data_service.get_realtime_data(symbol)

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(symbol: str, period: str = "1d", limit: int = 100):
    """获取K线数据"""
    return {
        "symbol": symbol,
        "period": period,
        "data": data_service.get_kline_data(symbol, period, limit)
    }

# ==================== 交易端点 ====================

@app.get("/api/v1/trading/positions")
async def get_positions():
    """获取持仓"""
    if not app.state.positions:
        # 返回模拟数据
        app.state.positions = [
            {
                "id": 1,
                "symbol": "000001.SZ",
                "name": "平安银行",
                "quantity": 1000,
                "avg_price": 10.50,
                "current_price": 10.58,
                "profit": 80,
                "profit_rate": 0.76
            },
            {
                "id": 2,
                "symbol": "600519.SH",
                "name": "贵州茅台",
                "quantity": 10,
                "avg_price": 1700.00,
                "current_price": 1685.00,
                "profit": -150,
                "profit_rate": -0.88
            }
        ]
    return {"data": app.state.positions, "total": len(app.state.positions)}

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单"""
    if not app.state.orders:
        # 返回模拟数据
        app.state.orders = [
            {
                "id": 1,
                "symbol": "000001.SZ",
                "side": "buy",
                "quantity": 1000,
                "price": 10.50,
                "status": "filled",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": 2,
                "symbol": "600519.SH",
                "side": "buy",
                "quantity": 10,
                "price": 1700.00,
                "status": "filled",
                "created_at": datetime.now().isoformat()
            }
        ]
    return {"data": app.state.orders, "total": len(app.state.orders)}

@app.post("/api/v1/trading/orders")
async def create_order(order: OrderRequest):
    """创建订单"""
    new_order = {
        "id": len(app.state.orders) + 1,
        "symbol": order.symbol,
        "side": order.side,
        "quantity": order.quantity,
        "price": order.price,
        "order_type": order.order_type,
        "status": "pending",
        "created_at": datetime.now().isoformat()
    }
    app.state.orders.append(new_order)
    
    # 模拟订单执行
    new_order["status"] = "filled"
    
    return {"message": "订单创建成功", "order": new_order}

# ==================== 策略端点 ====================

@app.get("/api/v1/strategies")
async def get_strategies():
    """获取策略列表"""
    if not app.state.strategies:
        # 返回模拟策略
        app.state.strategies = [
            {
                "id": 1,
                "name": "均线策略",
                "description": "基于移动平均线的交易策略",
                "status": "running",
                "profit_rate": 15.8,
                "created_at": datetime.now().isoformat()
            },
            {
                "id": 2,
                "name": "网格交易",
                "description": "网格交易策略",
                "status": "stopped",
                "profit_rate": 8.5,
                "created_at": datetime.now().isoformat()
            }
        ]
    return {"data": app.state.strategies, "total": len(app.state.strategies)}

@app.post("/api/v1/strategies")
async def create_strategy(strategy: StrategyRequest):
    """创建策略"""
    new_strategy = {
        "id": len(app.state.strategies) + 1,
        "name": strategy.name,
        "code": strategy.code,
        "description": strategy.description,
        "parameters": strategy.parameters,
        "status": "created",
        "created_at": datetime.now().isoformat()
    }
    app.state.strategies.append(new_strategy)
    return {"message": "策略创建成功", "strategy": new_strategy}

# ==================== 回测端点 ====================

@app.post("/api/v1/backtest/run")
async def run_backtest(request: BacktestRequest):
    """运行回测"""
    # 模拟回测结果
    import random
    
    result = {
        "id": f"backtest_{datetime.now().timestamp()}",
        "strategy_id": request.strategy_id,
        "start_date": request.start_date,
        "end_date": request.end_date,
        "initial_capital": request.initial_capital,
        "final_capital": request.initial_capital * (1 + random.uniform(-0.2, 0.5)),
        "total_return": random.uniform(-20, 50),
        "sharpe_ratio": random.uniform(0.5, 2.5),
        "max_drawdown": random.uniform(5, 30),
        "win_rate": random.uniform(40, 70),
        "status": "completed",
        "created_at": datetime.now().isoformat()
    }
    
    return {"message": "回测开始", "result": result}

@app.get("/api/v1/backtest/results/{result_id}")
async def get_backtest_result(result_id: str):
    """获取回测结果"""
    # 模拟回测结果
    import random
    
    return {
        "id": result_id,
        "strategy_id": "1",
        "metrics": {
            "total_return": random.uniform(-20, 50),
            "sharpe_ratio": random.uniform(0.5, 2.5),
            "max_drawdown": random.uniform(5, 30),
            "win_rate": random.uniform(40, 70),
            "trade_count": random.randint(50, 500)
        },
        "equity_curve": [
            {"date": f"2024-{i:02d}-01", "value": 100000 * (1 + i * 0.01)}
            for i in range(1, 13)
        ],
        "status": "completed"
    }

# ==================== 风险管理端点 ====================

@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    import random
    
    return {
        "total_exposure": random.uniform(100000, 1000000),
        "var_95": random.uniform(1000, 10000),
        "cvar_95": random.uniform(2000, 20000),
        "sharpe_ratio": random.uniform(0.5, 2.5),
        "beta": random.uniform(0.8, 1.2),
        "alpha": random.uniform(-0.05, 0.15),
        "max_drawdown": random.uniform(5, 30),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/risk/limits")
async def get_risk_limits():
    """获取风险限制"""
    return {
        "max_position_size": 100000,
        "max_leverage": 2.0,
        "max_drawdown": 20,
        "stop_loss": 5,
        "daily_loss_limit": 10000,
        "timestamp": datetime.now().isoformat()
    }

# ==================== WebSocket端点 ====================

@app.websocket("/api/v1/ws/market")
async def websocket_market(websocket: WebSocket):
    """市场数据WebSocket"""
    await websocket.accept()
    app.state.websocket_connections.add(websocket)
    
    try:
        # 发送欢迎消息
        await websocket.send_json({
            "type": "connected",
            "message": "WebSocket连接成功",
            "timestamp": datetime.now().isoformat()
        })
        
        # 持续发送市场数据
        import asyncio
        while True:
            # 发送模拟市场数据
            await websocket.send_json({
                "type": "market_data",
                "data": data_service.get_realtime_data("000001.SZ"),
                "timestamp": datetime.now().isoformat()
            })
            await asyncio.sleep(1)  # 每秒发送一次
            
    except WebSocketDisconnect:
        app.state.websocket_connections.remove(websocket)
        logger.info("WebSocket连接断开")
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        app.state.websocket_connections.discard(websocket)

# ==================== 监控端点 ====================

@app.get("/api/v1/monitoring/system")
async def get_system_status():
    """获取系统状态"""
    import psutil
    
    return {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage": psutil.disk_usage('/').percent,
        "active_connections": len(app.state.websocket_connections),
        "total_orders": len(app.state.orders),
        "total_strategies": len(app.state.strategies),
        "timestamp": datetime.now().isoformat()
    }

# ==================== 存储管理端点 ====================

@app.get("/api/v1/storage/stats")
async def get_storage_stats():
    """获取存储统计"""
    return {
        "total_space": 1000000000,  # 1GB
        "used_space": 500000000,    # 500MB
        "free_space": 500000000,    # 500MB
        "usage_percent": 50.0,
        "file_count": 1234,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/compression/stats")
async def get_compression_stats():
    """获取压缩统计"""
    return {
        "original_size": 1000000000,
        "compressed_size": 300000000,
        "compression_ratio": 70.0,
        "saved_space": 700000000,
        "timestamp": datetime.now().isoformat()
    }

# ==================== API文档端点 ====================

@app.get("/api/v1/routes")
async def get_all_routes():
    """获取所有路由"""
    routes = []
    for route in app.routes:
        if hasattr(route, "path"):
            routes.append({
                "path": route.path,
                "methods": list(getattr(route, "methods", ["GET"])),
                "name": getattr(route, "name", "unknown")
            })
    
    return {
        "total": len(routes),
        "routes": sorted(routes, key=lambda x: x["path"])
    }

# ==================== 静态文件服务 ====================

# 检查前端构建目录
frontend_dist = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
    "frontend", "dist"
)

if os.path.exists(frontend_dist):
    app.mount("/assets", StaticFiles(directory=os.path.join(frontend_dist, "assets")), name="assets")
    
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        """为Vue SPA提供服务"""
        # 跳过API路径
        if full_path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")
        
        # 检查是否是静态资源
        static_file = os.path.join(frontend_dist, full_path)
        if os.path.exists(static_file) and os.path.isfile(static_file):
            return FileResponse(static_file)
        
        # 所有其他路径返回index.html（Vue Router处理）
        index_path = os.path.join(frontend_dist, "index.html")
        if os.path.exists(index_path):
            return FileResponse(index_path)
        
        raise HTTPException(status_code=404, detail="Page not found")

# ==================== 启动应用 ====================

if __name__ == "__main__":
    logger.info("启动量化投资平台后端服务...")
    
    # 运行服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )