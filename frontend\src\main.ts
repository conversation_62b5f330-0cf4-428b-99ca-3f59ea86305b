/**
 * Quantum Investment Platform - Main Entry Point
 * Full Vue 3 Application with Router, Pinia, and Element Plus
 */

console.log('🚀 Starting main.ts execution...')

// HMR Guard for Vite 6.x compatibility - MUST be first
if (import.meta.env.DEV && !import.meta.hot) {
  console.log('⚠️  Initializing HMR guard for Vite 6.x compatibility...')
  // Create minimal HMR context to prevent errors
  Object.defineProperty(import.meta, 'hot', {
    value: {
      accept: () => {},
      dispose: () => {},
      decline: () => {},
      invalidate: () => {},
      on: () => {},
      send: () => {},
      data: {}
    },
    writable: false,
    enumerable: true,
    configurable: false
  })
  console.log('✅ HMR guard initialized')
}

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import App from './App.vue'
// 临时使用最小路由以便验证渲染
import router from './router/minimal'

// Import global styles
import './assets/styles/index.scss'

console.log('✅ Dependencies imported successfully')

try {
  console.log('🔧 Creating Vue application...')
  // Create Vue application
  const app = createApp(App)
  console.log('✅ Vue app created')

  console.log('🔧 Creating Pinia store...')
  // Create Pinia store
  const pinia = createPinia()
  console.log('✅ Pinia store created')

  console.log('🔧 Installing plugins...')
  // Use plugins
  app.use(pinia)
  console.log('✅ Pinia installed')

  app.use(router)
  console.log('✅ Router installed')

  app.use(ElementPlus, {
    locale: 'zh-cn'
  })
  console.log('✅ Element Plus installed')

  console.log('🔧 Registering icons...')
  // Register all Element Plus icons
  let iconCount = 0
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
    iconCount++
  }
  console.log(`✅ Registered ${iconCount} Element Plus icons`)

  console.log('🔧 Configuring app...')
  // Configure global properties
  app.config.globalProperties.$apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

  // Global error handler
  app.config.errorHandler = (err, instance, info) => {
    console.error('❌ Vue Global error:', err)
    console.error('Component instance:', instance)
    console.error('Error info:', info)

    // Show error in UI for development
    if (import.meta.env.DEV) {
      const appElement = document.getElementById('app')
      if (appElement && !appElement.innerHTML.includes('Vue Error')) {
        appElement.innerHTML = `
          <div style="padding: 40px; background: #fee; border: 2px solid #f56c6c; margin: 20px;">
            <h2 style="color: #f56c6c;">Vue Error</h2>
            <p><strong>Error:</strong> ${err.message}</p>
            <p><strong>Info:</strong> ${info}</p>
            <details>
              <summary>Stack Trace</summary>
              <pre style="font-size: 12px; overflow-x: auto;">${err.stack}</pre>
            </details>
          </div>
        ` + appElement.innerHTML
      }
    }
  }

  // Performance monitoring
  if (import.meta.env.PROD) {
    app.config.performance = true
  }
  console.log('✅ App configured')

  console.log('🔧 Mounting application to #app...')
  // Mount application
  const appElement = document.getElementById('app')
  if (!appElement) {
    throw new Error('Could not find #app element to mount Vue application')
  }

  const mountResult = app.mount('#app')
  console.log('✅ Vue application mounted successfully:', mountResult)

  // Log successful initialization
  console.log('✅ Quantum Investment Platform initialized successfully')
  console.log('📊 Version:', import.meta.env.VITE_APP_VERSION || '1.0.0')
  console.log('🌐 API URL:', import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000')
  console.log('🚀 Environment:', import.meta.env.MODE)

  // Export app instance for debugging
  if (import.meta.env.DEV) {
    (window as any).__VUE_APP__ = app
    console.log('🔧 App instance exported to window.__VUE_APP__ for debugging')
  }

} catch (error) {
  console.error('❌ Vue application initialization failed:', error)
  console.error('Stack trace:', error.stack)

  // Show error in the app element
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif; background: #fee;">
        <h1 style="color: #f56c6c;">❌ Vue Application Failed to Initialize</h1>
        <p style="color: #666;">Error: ${error.message}</p>
        <div style="background: #fef0f0; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
          <h3>Stack Trace:</h3>
          <pre style="font-size: 12px; overflow-x: auto; white-space: pre-wrap;">${error.stack}</pre>
        </div>
        <p style="color: #999;">Check the browser console for more details.</p>
      </div>
    `
  }

  // Re-throw the error to stop execution
  throw error
}
