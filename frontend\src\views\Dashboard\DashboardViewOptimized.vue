<template>
  <div class="dashboard-view">
    <!-- 使用拆分的头部组件 -->
    <DashboardHeader
      :connection-status="connectionStatus"
      :connection-quality="connectionQuality"
      :current-time-range="timeRange"
      :refreshing="refreshing"
      @time-range-change="handleTimeRangeChange"
      @refresh="handleRefresh"
    />

    <!-- 使用拆分的指标网格组件 -->
    <MetricsGrid
      :account-metrics="accountMetrics"
      :portfolio-trend-data="portfolioTrendData"
      :daily-trend-data="dailyTrendData"
      :total-profit-trend-data="totalProfitTrendData"
      @metric-click="handleMetricClick"
    />

    <!-- 快速导航（懒加载） -->
    <Suspense>
      <template #default>
        <QuickNavigation />
      </template>
      <template #fallback>
        <el-skeleton :rows="3" animated />
      </template>
    </Suspense>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <el-row :gutter="20">
        <!-- 左侧区域 -->
        <el-col :span="16">
          <!-- 投资组合图表（使用优化的图表组件） -->
          <PortfolioChart
            :loading="isLoading"
            :metrics="accountMetrics"
            :time-range="timeRange"
          />

          <!-- 持仓列表（虚拟滚动优化） -->
          <PositionsList
            :positions="positions"
            :loading="isLoading"
          />
        </el-col>

        <!-- 右侧区域 -->
        <el-col :span="8">
          <!-- 快速交易面板 -->
          <QuickTradingPanel
            :account-metrics="accountMetrics"
          />

          <!-- 市场概览 -->
          <MarketOverview
            :indices="marketIndices"
            :get-price-flash-class="getPriceFlashClass"
          />

          <!-- 热门股票 -->
          <HotStocksList
            :stocks="topHotStocks"
            :get-price-flash-class="getPriceFlashClass"
          />

          <!-- 实时资讯 -->
          <RealtimeNewsFeed
            :news="latestNews"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 全局错误处理 -->
    <el-alert
      v-if="error"
      type="error"
      :title="error.message"
      :closable="true"
      @close="error = null"
      style="margin-top: 20px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

// 导入拆分的组件
import DashboardHeader from '@/components/dashboard/DashboardHeader.vue'
import MetricsGrid from '@/components/dashboard/MetricsGrid.vue'
import PortfolioChart from '@/components/dashboard/PortfolioChart.vue'
import PositionsList from '@/components/dashboard/PositionsList.vue'
import QuickTradingPanel from '@/components/dashboard/QuickTradingPanel.vue'
import MarketOverview from '@/components/dashboard/MarketOverview.vue'
import HotStocksList from '@/components/dashboard/HotStocksList.vue'
import RealtimeNewsFeed from '@/components/dashboard/RealtimeNewsFeed.vue'

// 懒加载组件
const QuickNavigation = defineAsyncComponent(
  () => import('@/components/dashboard/QuickNavigation.vue')
)

// 使用优化的数据管理Hook
import { useDashboardData } from '@/composables/dashboard/useDashboardData'
import { formatCurrency } from '@/utils/format/financial'

const router = useRouter()

// 使用统一的数据管理
const {
  isLoading,
  refreshing,
  error,
  isConnected,
  connectionStatus,
  connectionQuality,
  accountMetrics,
  marketIndices,
  topHotStocks,
  latestNews,
  positions,
  portfolioTrend,
  refreshData,
  loadTimeRangeData,
  getPriceFlashClass
} = useDashboardData()

// 本地状态
const timeRange = ref<'today' | 'week' | 'month'>('today')

// 生成趋势数据（优化：使用计算属性缓存）
const portfolioTrendData = computed(() => {
  if (portfolioTrend.value?.length > 0) {
    return portfolioTrend.value.map(item => item.value)
  }
  // 返回简单的默认数据，而不是复杂的模拟数据
  return [100000, 98000, 102000]
})

const dailyTrendData = computed(() => {
  // 基于实际数据生成，避免硬编码
  const dailyProfit = accountMetrics.value.dailyProfit
  return Array(24).fill(0).map((_, i) => 
    dailyProfit * (0.5 + Math.random()) * (i / 24)
  )
})

const totalProfitTrendData = computed(() => {
  // 基于实际数据生成
  const totalProfit = accountMetrics.value.totalProfit
  return Array(12).fill(0).map((_, i) => 
    totalProfit * (i / 12) * (0.8 + Math.random() * 0.4)
  )
})

// 事件处理（优化：避免不必要的刷新）
const handleTimeRangeChange = async (range: 'today' | 'week' | 'month') => {
  if (timeRange.value === range) return
  
  timeRange.value = range
  try {
    await loadTimeRangeData(range)
    ElMessage.success(`已切换到${range === 'today' ? '今日' : range === 'week' ? '本周' : '本月'}数据`)
  } catch (error) {
    ElMessage.error('切换时间范围失败')
  }
}

const handleRefresh = async () => {
  try {
    await refreshData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败，请重试')
  }
}

const handleMetricClick = (metricId: string) => {
  const routeMap: Record<string, string> = {
    portfolio: '/portfolio',
    daily: '/trading/history',
    total: '/analytics/profit',
    position: '/trading/positions'
  }
  
  const route = routeMap[metricId]
  if (route) {
    router.push(route)
    ElMessage.info(`正在跳转到${metricId === 'portfolio' ? '投资组合' : metricId === 'daily' ? '今日交易' : metricId === 'total' ? '盈亏分析' : '持仓详情'}`)
  }
}
</script>

<style scoped>
.dashboard-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.dashboard-content {
  margin-top: 24px;
}

/* 性能优化：使用 CSS containment */
.dashboard-content > * {
  contain: layout style;
}

/* 减少重绘：固定高度的容器 */
.chart-container {
  height: 400px;
  will-change: transform;
}

/* 优化动画性能 */
@media (prefers-reduced-motion: no-preference) {
  .dashboard-view * {
    transition-duration: 0.2s;
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .dashboard-view {
    padding: 10px;
  }
  
  .el-col {
    margin-bottom: 20px;
  }
}
</style>