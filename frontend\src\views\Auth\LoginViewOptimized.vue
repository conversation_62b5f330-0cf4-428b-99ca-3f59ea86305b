<template>
  <div class="login-page">
    <div class="login-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="brand-logo">
            <el-icon size="48"><TrendCharts /></el-icon>
          </div>
          <h1 class="brand-title">量化投资平台</h1>
          <p class="brand-subtitle">专业的量化交易与投资管理系统</p>

          <div class="features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>实时行情数据</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>智能策略回测</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>风险控制管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>投资组合优化</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>{{ isLogin ? '登录账户' : '注册账户' }}</h2>
            <p>{{ isLogin ? '欢迎回来，请登录您的账户' : '创建新账户开始量化投资之旅' }}</p>
          </div>

          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            class="login-form"
            size="large"
            @submit.prevent="handleSubmit"
          >
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input
                v-model="formData.username"
                :placeholder="isLogin ? '用户名' : '用户名'"
                :prefix-icon="User"
                clearable
              />
            </el-form-item>

            <!-- 邮箱（仅注册时显示） -->
            <el-form-item v-if="!isLogin" prop="email">
              <el-input
                v-model="formData.email"
                placeholder="邮箱地址（可选）"
                :prefix-icon="Message"
                clearable
              />
            </el-form-item>

            <!-- 密码 -->
            <el-form-item prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                show-password
                placeholder="请输入密码"
                :prefix-icon="Lock"
                clearable
              />
            </el-form-item>

            <!-- 确认密码（仅注册时显示） -->
            <el-form-item v-if="!isLogin" prop="confirmPassword">
              <el-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="确认密码"
                :prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>

            <!-- 记住我 & 忘记密码 -->
            <div class="form-options" v-if="isLogin">
              <el-checkbox v-model="formData.remember">记住我</el-checkbox>
              <el-button link @click="showForgotPassword">忘记密码？</el-button>
            </div>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                class="submit-button"
                :loading="loading"
                @click="handleSubmit"
              >
                {{ isLogin ? '登录' : '注册' }}
              </el-button>
            </el-form-item>

            <!-- 快速登录按钮 -->
            <div class="quick-login-buttons" v-if="isLogin">
              <el-button
                type="success"
                class="submit-button"
                :loading="loading"
                @click="handleQuickLogin('admin')"
              >
                管理员登录 (admin/admin)
              </el-button>
              <el-button
                type="warning"
                class="submit-button"
                :loading="loading"
                @click="handleQuickLogin('demo')"
                plain
              >
                演示账号 (demo/demo)
              </el-button>
            </div>

            <!-- 切换登录/注册 -->
            <div class="form-switch">
              <span>{{ isLogin ? '没有账户？' : '已有账户？' }}</span>
              <el-button link @click="toggleMode">
                {{ isLogin ? '立即注册' : '立即登录' }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { User, Lock, Message, Check, TrendCharts } from '@element-plus/icons-vue'
import { optimizedAPI } from '@/api/optimized-api'

// 路由
const router = useRouter()

// 表单引用
const formRef = ref()

// 状态
const isLogin = ref(true)
const loading = ref(false)

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  email: '',
  confirmPassword: '',
  remember: false,
})

// 表单验证规则
const formRules = computed(() => {
  const rules: any = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 4, max: 20, message: '密码长度在 4 到 20 个字符', trigger: 'blur' }
    ],
  }

  if (!isLogin.value) {
    rules.email = [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
    rules.confirmPassword = [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value !== formData.password) {
            callback(new Error('两次输入密码不一致!'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  return rules
})

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  loading.value = true

  try {
    if (isLogin.value) {
      // 登录
      const response = await optimizedAPI.auth.login(formData.username, formData.password)
      
      ElNotification({
        title: '登录成功',
        message: `欢迎回来，${response.user_info?.username || formData.username}！`,
        type: 'success',
        duration: 2000
      })

      // 跳转到首页
      setTimeout(() => {
        router.push('/')
      }, 500)
    } else {
      // 注册
      const response = await optimizedAPI.auth.register(
        formData.username,
        formData.password,
        formData.email || undefined
      )
      
      ElMessage.success('注册成功，请登录')
      
      // 切换到登录模式
      isLogin.value = true
      formData.password = ''
      formData.confirmPassword = ''
    }
  } catch (error: any) {
    console.error('操作失败:', error)
    ElMessage.error(error.response?.data?.detail || error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// 快速登录
const handleQuickLogin = async (type: 'admin' | 'demo') => {
  loading.value = true

  try {
    const credentials = {
      admin: { username: 'admin', password: 'admin' },
      demo: { username: 'demo', password: 'demo' }
    }

    const { username, password } = credentials[type]
    const response = await optimizedAPI.auth.login(username, password)

    ElNotification({
      title: '登录成功',
      message: `欢迎使用${type === 'admin' ? '管理员' : '演示'}账号`,
      type: 'success',
      duration: 2000
    })

    // 跳转到首页
    setTimeout(() => {
      router.push('/')
    }, 500)
  } catch (error: any) {
    console.error('快速登录失败:', error)
    ElMessage.error('快速登录失败，请检查后端服务是否正常运行')
  } finally {
    loading.value = false
  }
}

// 忘记密码
const showForgotPassword = () => {
  ElMessage.info('请联系管理员重置密码')
}
</script>

<style scoped lang="scss">
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  display: flex;
  width: 90%;
  max-width: 1100px;
  min-height: 600px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;

  .brand-content {
    text-align: center;
  }

  .brand-logo {
    margin-bottom: 30px;
    
    .el-icon {
      font-size: 64px;
    }
  }

  .brand-title {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .brand-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin-bottom: 40px;
  }

  .features {
    text-align: left;
    display: inline-block;
  }

  .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;

    .el-icon {
      margin-right: 10px;
      color: #4ade80;
    }
  }
}

.form-section {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  .form-container {
    width: 100%;
    max-width: 400px;
  }

  .form-header {
    text-align: center;
    margin-bottom: 40px;

    h2 {
      font-size: 28px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }

    p {
      color: #666;
      font-size: 14px;
    }
  }

  .login-form {
    .el-form-item {
      margin-bottom: 24px;
    }

    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .submit-button {
      width: 100%;
      height: 44px;
      font-size: 16px;
    }

    .quick-login-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 20px;
    }

    .form-switch {
      text-align: center;
      color: #666;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    width: 95%;
  }

  .brand-section {
    padding: 40px 20px;
  }

  .form-section {
    padding: 40px 20px;
  }
}
</style>