/**
 * 最简化的入口文件 - 用于修复页面空白问题
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 创建一个最简单的App组件
const SimpleApp = {
  template: `
    <div id="app-root" style="min-height: 100vh; background: #f5f7fa;">
      <el-container style="height: 100vh;">
        <!-- 侧边栏 -->
        <el-aside width="200px" style="background-color: #545c64;">
          <div style="padding: 20px; color: white; font-size: 18px; text-align: center;">
            量化投资平台
          </div>
          <el-menu
            default-active="1"
            background-color="#545c64"
            text-color="#fff"
            active-text-color="#ffd04b"
            @select="handleMenuSelect"
          >
            <el-menu-item index="1">
              <el-icon><HomeFilled /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="2">
              <el-icon><TrendCharts /></el-icon>
              <span>市场行情</span>
            </el-menu-item>
            <el-menu-item index="3">
              <el-icon><Coin /></el-icon>
              <span>交易中心</span>
            </el-menu-item>
            <el-menu-item index="4">
              <el-icon><DataAnalysis /></el-icon>
              <span>策略中心</span>
            </el-menu-item>
            <el-menu-item index="5">
              <el-icon><Warning /></el-icon>
              <span>风险管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-container>
          <!-- 顶部导航栏 -->
          <el-header style="background-color: white; display: flex; align-items: center; justify-content: space-between; border-bottom: 1px solid #e4e7ed;">
            <div style="font-size: 20px; font-weight: bold;">{{ currentPageTitle }}</div>
            <div style="display: flex; align-items: center; gap: 20px;">
              <el-badge :value="3">
                <el-icon :size="20"><Bell /></el-icon>
              </el-badge>
              <el-dropdown>
                <span style="cursor: pointer; display: flex; align-items: center; gap: 10px;">
                  <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                  <span>管理员</span>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>个人中心</el-dropdown-item>
                    <el-dropdown-item>系统设置</el-dropdown-item>
                    <el-dropdown-item divided>退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-header>
          
          <!-- 主要内容 -->
          <el-main style="padding: 20px;">
            <div v-if="currentPage === '1'">
              <h2>仪表盘</h2>
              <el-row :gutter="20" style="margin-top: 20px;">
                <el-col :span="6">
                  <el-card>
                    <el-statistic title="总资产" :value="1234567.89" prefix="¥" />
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card>
                    <el-statistic title="今日盈亏" :value="12345.67" prefix="¥" :value-style="{ color: '#67c23a' }" />
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card>
                    <el-statistic title="持仓数量" :value="15" suffix="只" />
                  </el-card>
                </el-col>
                <el-col :span="6">
                  <el-card>
                    <el-statistic title="胜率" :value="68.5" suffix="%" />
                  </el-card>
                </el-col>
              </el-row>
              
              <el-card style="margin-top: 20px;">
                <template #header>
                  <div>最新交易</div>
                </template>
                <el-table :data="mockTrades" style="width: 100%">
                  <el-table-column prop="time" label="时间" width="180" />
                  <el-table-column prop="code" label="代码" width="100" />
                  <el-table-column prop="name" label="名称" width="100" />
                  <el-table-column prop="type" label="类型" width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.type === '买入' ? 'success' : 'danger'">
                        {{ scope.row.type }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="price" label="价格" />
                  <el-table-column prop="quantity" label="数量" />
                  <el-table-column prop="amount" label="金额" />
                </el-table>
              </el-card>
            </div>
            
            <div v-else-if="currentPage === '2'">
              <h2>市场行情</h2>
              <el-card style="margin-top: 20px;">
                <el-table :data="mockStocks" style="width: 100%">
                  <el-table-column prop="code" label="代码" width="100" />
                  <el-table-column prop="name" label="名称" width="120" />
                  <el-table-column prop="price" label="现价" />
                  <el-table-column prop="change" label="涨跌幅">
                    <template #default="scope">
                      <span :style="{ color: scope.row.change > 0 ? '#f56c6c' : '#67c23a' }">
                        {{ scope.row.change > 0 ? '+' : '' }}{{ scope.row.change }}%
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="volume" label="成交量" />
                  <el-table-column prop="amount" label="成交额" />
                </el-table>
              </el-card>
            </div>
            
            <div v-else-if="currentPage === '3'">
              <h2>交易中心</h2>
              <el-card style="margin-top: 20px;">
                <el-form :model="tradeForm" label-width="100px">
                  <el-form-item label="股票代码">
                    <el-input v-model="tradeForm.code" placeholder="请输入股票代码" />
                  </el-form-item>
                  <el-form-item label="交易类型">
                    <el-radio-group v-model="tradeForm.type">
                      <el-radio label="buy">买入</el-radio>
                      <el-radio label="sell">卖出</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="价格">
                    <el-input-number v-model="tradeForm.price" :min="0" :step="0.01" />
                  </el-form-item>
                  <el-form-item label="数量">
                    <el-input-number v-model="tradeForm.quantity" :min="100" :step="100" />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleTrade">提交订单</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </div>
            
            <div v-else-if="currentPage === '4'">
              <h2>策略中心</h2>
              <el-card style="margin-top: 20px;">
                <el-empty description="策略功能开发中..." />
              </el-card>
            </div>
            
            <div v-else-if="currentPage === '5'">
              <h2>风险管理</h2>
              <el-card style="margin-top: 20px;">
                <el-empty description="风险管理功能开发中..." />
              </el-card>
            </div>
          </el-main>
        </el-container>
      </el-container>
    </div>
  `,
  data() {
    return {
      currentPage: '1',
      currentPageTitle: '仪表盘',
      tradeForm: {
        code: '',
        type: 'buy',
        price: 0,
        quantity: 100
      },
      mockTrades: [
        { time: '2025-01-11 09:30:00', code: '000001', name: '平安银行', type: '买入', price: 10.50, quantity: 1000, amount: 10500 },
        { time: '2025-01-11 09:35:00', code: '000002', name: '万科A', type: '卖出', price: 15.20, quantity: 500, amount: 7600 },
        { time: '2025-01-11 10:00:00', code: '000858', name: '五粮液', type: '买入', price: 168.50, quantity: 200, amount: 33700 }
      ],
      mockStocks: [
        { code: '000001', name: '平安银行', price: 10.50, change: 2.3, volume: '1.2亿', amount: '12.6亿' },
        { code: '000002', name: '万科A', price: 15.20, change: -1.5, volume: '8500万', amount: '12.9亿' },
        { code: '000858', name: '五粮液', price: 168.50, change: 0.8, volume: '2300万', amount: '38.8亿' },
        { code: '600519', name: '贵州茅台', price: 1680.00, change: 1.2, volume: '350万', amount: '588亿' }
      ]
    }
  },
  methods: {
    handleMenuSelect(index: string) {
      this.currentPage = index
      const titles: Record<string, string> = {
        '1': '仪表盘',
        '2': '市场行情',
        '3': '交易中心',
        '4': '策略中心',
        '5': '风险管理'
      }
      this.currentPageTitle = titles[index] || '仪表盘'
    },
    handleTrade() {
      ElMessage.success('订单提交成功（模拟）')
    }
  }
}

// 初始化应用
console.log('🚀 正在初始化量化投资平台（简化版）...')

try {
  const app = createApp(SimpleApp)
  
  // 使用Pinia状态管理
  const pinia = createPinia()
  app.use(pinia)
  
  // 使用Element Plus
  app.use(ElementPlus)
  
  // 挂载应用
  app.mount('#app')
  
  console.log('✅ 应用启动成功！')
  
  // 在控制台输出一些帮助信息
  console.log('%c量化投资平台已启动', 'color: #67c23a; font-size: 16px; font-weight: bold;')
  console.log('当前版本：简化修复版 v1.0')
  console.log('如遇问题，请尝试刷新页面或清除浏览器缓存')
  
} catch (error) {
  console.error('❌ 应用启动失败:', error)
  
  // 显示错误信息
  const appEl = document.getElementById('app')
  if (appEl) {
    appEl.innerHTML = `
      <div style="padding: 40px; text-align: center;">
        <h1 style="color: #f56c6c;">应用加载失败</h1>
        <p>错误: ${error.message}</p>
        <button onclick="location.reload()" style="padding: 10px 20px; background: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">
          刷新页面重试
        </button>
      </div>
    `
  }
}