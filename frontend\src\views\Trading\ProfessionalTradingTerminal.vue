<template>
  <div class="professional-trading-terminal" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 顶部工具栏 -->
    <div class="terminal-toolbar">
      <div class="toolbar-left">
        <!-- 股票搜索 -->
        <div class="symbol-search">
          <el-autocomplete
            v-model="searchKeyword"
            :fetch-suggestions="searchStocks"
            :trigger-on-focus="false"
            placeholder="搜索股票代码/名称/拼音"
            style="width: 280px"
            @select="handleStockSelect"
            @keyup.enter="quickSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #default="{ item }">
              <div class="stock-suggestion-item">
                <div class="stock-main">
                  <span class="stock-code">{{ item.symbol }}</span>
                  <span class="stock-name">{{ item.name }}</span>
                </div>
                <div class="stock-price-info">
                  <span class="price">¥{{ item.price.toFixed(2) }}</span>
                  <span class="change" :class="item.change >= 0 ? 'up' : 'down'">
                    {{ item.change >= 0 ? '+' : '' }}{{ item.changePercent.toFixed(2) }}%
                  </span>
                </div>
              </div>
            </template>
          </el-autocomplete>
        </div>

        <!-- 快速切换标的 -->
        <div class="quick-symbols">
          <el-tag
            v-for="stock in watchlist"
            :key="stock.symbol"
            :type="currentSymbol === stock.symbol ? 'primary' : 'info'"
            @click="switchSymbol(stock.symbol)"
            closable
            @close="removeFromWatchlist(stock.symbol)"
            class="symbol-tag"
          >
            <span class="tag-symbol">{{ stock.symbol }}</span>
            <span class="tag-price" :class="stock.change >= 0 ? 'up' : 'down'">
              {{ stock.price.toFixed(2) }}
            </span>
          </el-tag>
          <el-button
            v-if="watchlist.length < 10"
            type="text"
            size="small"
            @click="showWatchlistManager = true"
          >
            <el-icon><Plus /></el-icon>
            添加
          </el-button>
        </div>
      </div>

      <div class="toolbar-center">
        <!-- 市场状态指示器 -->
        <div class="market-status">
          <div class="status-indicator" :class="`status-${marketStatus}`">
            <span class="status-dot"></span>
            <span class="status-text">{{ marketStatusText }}</span>
          </div>
          <div class="market-time">{{ currentTime }}</div>
        </div>
      </div>

      <div class="toolbar-right">
        <!-- 布局切换 -->
        <el-dropdown trigger="click" @command="handleLayoutChange">
          <el-button type="text">
            <el-icon><Grid /></el-icon>
            布局
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="default">
                <el-icon><Monitor /></el-icon>
                默认布局
              </el-dropdown-item>
              <el-dropdown-item command="focus">
                <el-icon><Aim /></el-icon>
                聚焦模式
              </el-dropdown-item>
              <el-dropdown-item command="multi">
                <el-icon><CopyDocument /></el-icon>
                多屏模式
              </el-dropdown-item>
              <el-dropdown-item command="custom" divided>
                <el-icon><Setting /></el-icon>
                自定义布局
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 设置 -->
        <el-button type="text" @click="showSettings = true">
          <el-icon><Setting /></el-icon>
        </el-button>

        <!-- 全屏 -->
        <el-button type="text" @click="toggleFullscreen">
          <el-icon><component :is="isFullscreen ? 'Close' : 'FullScreen'" /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 主要交易区域 -->
    <div class="terminal-workspace" :class="`layout-${currentLayout}`">
      <!-- 左侧：图表和深度 -->
      <div class="workspace-left">
        <!-- 高级K线图表 -->
        <div class="chart-panel panel">
          <div class="panel-header">
            <div class="panel-title">
              <span class="symbol-info">
                <strong>{{ currentStock.symbol }}</strong>
                <span class="name">{{ currentStock.name }}</span>
                <span class="price" :class="priceDirection">
                  ¥{{ currentStock.price.toFixed(2) }}
                </span>
                <span class="change" :class="priceDirection">
                  {{ currentStock.change >= 0 ? '+' : '' }}{{ currentStock.change.toFixed(2) }}
                  ({{ currentStock.changePercent.toFixed(2) }}%)
                </span>
              </span>
            </div>
            <div class="panel-tools">
              <!-- 周期选择 -->
              <el-button-group size="small">
                <el-button
                  v-for="period in periods"
                  :key="period.value"
                  :type="selectedPeriod === period.value ? 'primary' : 'default'"
                  @click="changePeriod(period.value)"
                >
                  {{ period.label }}
                </el-button>
              </el-button-group>

              <!-- 图表类型 -->
              <el-button-group size="small" style="margin-left: 10px">
                <el-button
                  :type="chartType === 'candle' ? 'primary' : 'default'"
                  @click="chartType = 'candle'"
                >
                  K线
                </el-button>
                <el-button
                  :type="chartType === 'line' ? 'primary' : 'default'"
                  @click="chartType = 'line'"
                >
                  分时
                </el-button>
              </el-button-group>

              <!-- 指标管理 -->
              <el-dropdown trigger="click" style="margin-left: 10px">
                <el-button size="small">
                  指标
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="indicator in availableIndicators"
                      :key="indicator.id"
                      @click="toggleIndicator(indicator.id)"
                    >
                      <el-checkbox :model-value="activeIndicators.includes(indicator.id)">
                        {{ indicator.name }}
                      </el-checkbox>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 画图工具 -->
              <el-button-group size="small" style="margin-left: 10px">
                <el-button
                  v-for="tool in drawingTools"
                  :key="tool.id"
                  :type="selectedTool === tool.id ? 'primary' : 'default'"
                  @click="selectDrawingTool(tool.id)"
                  :title="tool.name"
                >
                  <el-icon><component :is="tool.icon" /></el-icon>
                </el-button>
              </el-button-group>
            </div>
          </div>

          <!-- 图表容器 -->
          <div class="chart-container" ref="chartContainer">
            <AdvancedTradingChart
              :symbol="currentSymbol"
              :period="selectedPeriod"
              :type="chartType"
              :indicators="activeIndicators"
              :drawing-tool="selectedTool"
              :height="chartHeight"
              @price-click="handlePriceClick"
              @drawing-complete="handleDrawingComplete"
            />
          </div>

          <!-- 图表底部信息栏 -->
          <div class="chart-info-bar">
            <div class="info-item">
              <span class="label">开:</span>
              <span class="value">{{ currentStock.open.toFixed(2) }}</span>
            </div>
            <div class="info-item">
              <span class="label">高:</span>
              <span class="value">{{ currentStock.high.toFixed(2) }}</span>
            </div>
            <div class="info-item">
              <span class="label">低:</span>
              <span class="value">{{ currentStock.low.toFixed(2) }}</span>
            </div>
            <div class="info-item">
              <span class="label">收:</span>
              <span class="value">{{ currentStock.close.toFixed(2) }}</span>
            </div>
            <div class="info-item">
              <span class="label">量:</span>
              <span class="value">{{ formatVolume(currentStock.volume) }}</span>
            </div>
            <div class="info-item">
              <span class="label">额:</span>
              <span class="value">{{ formatAmount(currentStock.amount) }}</span>
            </div>
            <div class="info-item">
              <span class="label">换手:</span>
              <span class="value">{{ currentStock.turnover.toFixed(2) }}%</span>
            </div>
            <div class="info-item">
              <span class="label">振幅:</span>
              <span class="value">{{ currentStock.amplitude.toFixed(2) }}%</span>
            </div>
          </div>
        </div>

        <!-- 深度和成交明细 -->
        <div class="market-depth-panel panel">
          <el-tabs v-model="depthTab" type="card">
            <!-- 十档盘口 -->
            <el-tab-pane label="盘口" name="orderbook">
              <div class="orderbook-container">
                <div class="orderbook-header">
                  <span>价格</span>
                  <span>数量</span>
                  <span>累计</span>
                </div>
                
                <!-- 卖盘 -->
                <div class="asks-list">
                  <div
                    v-for="(ask, index) in orderbook.asks"
                    :key="`ask-${index}`"
                    class="order-row ask"
                    @click="setOrderPrice(ask.price)"
                  >
                    <span class="price">{{ ask.price.toFixed(2) }}</span>
                    <span class="volume">{{ formatVolume(ask.volume) }}</span>
                    <span class="cumulative">{{ formatVolume(ask.cumulative) }}</span>
                    <div class="volume-bar" :style="{ width: getVolumeBarWidth(ask.volume, 'ask') }"></div>
                  </div>
                </div>

                <!-- 当前价格分割线 -->
                <div class="current-price-divider">
                  <span class="current-price" :class="priceDirection">
                    {{ currentStock.price.toFixed(2) }}
                  </span>
                  <span class="price-change" :class="priceDirection">
                    {{ currentStock.change >= 0 ? '↑' : '↓' }}
                    {{ Math.abs(currentStock.changePercent).toFixed(2) }}%
                  </span>
                </div>

                <!-- 买盘 -->
                <div class="bids-list">
                  <div
                    v-for="(bid, index) in orderbook.bids"
                    :key="`bid-${index}`"
                    class="order-row bid"
                    @click="setOrderPrice(bid.price)"
                  >
                    <span class="price">{{ bid.price.toFixed(2) }}</span>
                    <span class="volume">{{ formatVolume(bid.volume) }}</span>
                    <span class="cumulative">{{ formatVolume(bid.cumulative) }}</span>
                    <div class="volume-bar" :style="{ width: getVolumeBarWidth(bid.volume, 'bid') }"></div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 成交明细 -->
            <el-tab-pane label="成交" name="trades">
              <div class="trades-container">
                <div class="trades-header">
                  <span>时间</span>
                  <span>价格</span>
                  <span>数量</span>
                  <span>方向</span>
                </div>
                <div class="trades-list" ref="tradesListRef">
                  <div
                    v-for="trade in recentTrades"
                    :key="trade.id"
                    class="trade-row"
                    :class="trade.side"
                  >
                    <span class="time">{{ formatTradeTime(trade.timestamp) }}</span>
                    <span class="price">{{ trade.price.toFixed(2) }}</span>
                    <span class="volume">{{ formatVolume(trade.volume) }}</span>
                    <span class="side-indicator">
                      {{ trade.side === 'buy' ? 'B' : 'S' }}
                    </span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 资金流向 -->
            <el-tab-pane label="资金" name="flow">
              <div class="capital-flow">
                <div class="flow-summary">
                  <div class="flow-item inflow">
                    <span class="label">流入:</span>
                    <span class="value">{{ formatAmount(capitalFlow.inflow) }}</span>
                  </div>
                  <div class="flow-item outflow">
                    <span class="label">流出:</span>
                    <span class="value">{{ formatAmount(capitalFlow.outflow) }}</span>
                  </div>
                  <div class="flow-item net">
                    <span class="label">净流入:</span>
                    <span class="value" :class="capitalFlow.net >= 0 ? 'positive' : 'negative'">
                      {{ formatAmount(capitalFlow.net) }}
                    </span>
                  </div>
                </div>
                
                <!-- 资金流向图表 -->
                <div class="flow-chart" ref="flowChartRef"></div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 中间：下单面板 -->
      <div class="workspace-center">
        <!-- 快速下单面板 -->
        <div class="order-panel panel">
          <div class="panel-header">
            <div class="panel-title">快速下单</div>
            <div class="account-selector">
              <el-select v-model="selectedAccount" size="small" style="width: 150px">
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.name"
                  :value="account.id"
                >
                  <div class="account-option">
                    <span class="account-name">{{ account.name }}</span>
                    <span class="account-balance">¥{{ formatAmount(account.balance) }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>
          </div>

          <!-- 买卖切换 -->
          <div class="order-side-tabs">
            <div
              class="side-tab buy"
              :class="{ active: orderSide === 'buy' }"
              @click="orderSide = 'buy'"
            >
              <span class="tab-label">买入</span>
              <span class="tab-shortcut">F1</span>
            </div>
            <div
              class="side-tab sell"
              :class="{ active: orderSide === 'sell' }"
              @click="orderSide = 'sell'"
            >
              <span class="tab-label">卖出</span>
              <span class="tab-shortcut">F2</span>
            </div>
          </div>

          <!-- 下单表单 -->
          <div class="order-form">
            <!-- 订单类型 -->
            <div class="form-row">
              <el-radio-group v-model="orderType" size="small">
                <el-radio-button label="limit">限价</el-radio-button>
                <el-radio-button label="market">市价</el-radio-button>
                <el-radio-button label="stop">止损</el-radio-button>
                <el-radio-button label="conditional">条件单</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 价格输入 -->
            <div class="form-row" v-if="orderType !== 'market'">
              <div class="input-group">
                <span class="input-label">价格</span>
                <el-input-number
                  v-model="orderPrice"
                  :precision="2"
                  :step="0.01"
                  :min="0"
                  controls-position="right"
                  @focus="handlePriceFocus"
                  @keyup.up="adjustPrice(0.01)"
                  @keyup.down="adjustPrice(-0.01)"
                />
                <div class="quick-price-buttons">
                  <el-button size="small" @click="setOrderPrice(currentStock.bid1)">买一</el-button>
                  <el-button size="small" @click="setOrderPrice(currentStock.ask1)">卖一</el-button>
                  <el-button size="small" @click="setOrderPrice(currentStock.price)">现价</el-button>
                </div>
              </div>
            </div>

            <!-- 数量输入 -->
            <div class="form-row">
              <div class="input-group">
                <span class="input-label">数量</span>
                <el-input-number
                  v-model="orderQuantity"
                  :step="100"
                  :min="100"
                  :max="maxOrderQuantity"
                  controls-position="right"
                  @keyup.up="adjustQuantity(100)"
                  @keyup.down="adjustQuantity(-100)"
                />
                <div class="quick-quantity-buttons">
                  <el-button size="small" @click="setQuantityRatio(0.25)">1/4</el-button>
                  <el-button size="small" @click="setQuantityRatio(0.5)">1/2</el-button>
                  <el-button size="small" @click="setQuantityRatio(0.75)">3/4</el-button>
                  <el-button size="small" @click="setQuantityRatio(1)">全仓</el-button>
                </div>
              </div>
            </div>

            <!-- 金额显示 -->
            <div class="form-row">
              <div class="amount-display">
                <span class="label">金额:</span>
                <span class="value">¥{{ orderAmount.toFixed(2) }}</span>
              </div>
              <div class="available-display">
                <span class="label">可用:</span>
                <span class="value">
                  {{ orderSide === 'buy' 
                    ? `¥${availableBalance.toFixed(2)}` 
                    : `${availableShares}股` 
                  }}
                </span>
              </div>
            </div>

            <!-- 高级选项 -->
            <el-collapse v-model="advancedOptionsVisible">
              <el-collapse-item name="advanced">
                <template #title>
                  <span class="advanced-title">高级选项</span>
                </template>
                
                <!-- 止损止盈 -->
                <div class="advanced-options">
                  <div class="option-row">
                    <el-checkbox v-model="stopLossEnabled">止损</el-checkbox>
                    <el-input-number
                      v-if="stopLossEnabled"
                      v-model="stopLossPrice"
                      :precision="2"
                      :step="0.01"
                      size="small"
                      style="width: 120px"
                    />
                  </div>
                  <div class="option-row">
                    <el-checkbox v-model="takeProfitEnabled">止盈</el-checkbox>
                    <el-input-number
                      v-if="takeProfitEnabled"
                      v-model="takeProfitPrice"
                      :precision="2"
                      :step="0.01"
                      size="small"
                      style="width: 120px"
                    />
                  </div>
                  <div class="option-row">
                    <el-checkbox v-model="icebergEnabled">冰山委托</el-checkbox>
                    <el-input-number
                      v-if="icebergEnabled"
                      v-model="icebergQty"
                      :step="100"
                      size="small"
                      style="width: 120px"
                      placeholder="显示数量"
                    />
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>

            <!-- 下单按钮 -->
            <div class="form-actions">
              <el-button
                :type="orderSide === 'buy' ? 'success' : 'danger'"
                size="large"
                style="width: 100%"
                @click="submitOrder"
                :loading="submittingOrder"
              >
                {{ orderSide === 'buy' ? '买入' : '卖出' }} {{ currentStock.symbol }}
              </el-button>
            </div>

            <!-- 快捷键提示 -->
            <div class="shortcut-hints">
              <span class="hint">Enter: 下单</span>
              <span class="hint">Esc: 取消</span>
              <span class="hint">↑↓: 调整价格</span>
              <span class="hint">←→: 调整数量</span>
            </div>
          </div>
        </div>

        <!-- 持仓信息 -->
        <div class="position-panel panel">
          <div class="panel-header">
            <div class="panel-title">当前持仓</div>
            <el-button type="text" size="small" @click="refreshPositions">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>

          <div class="position-list">
            <div v-if="positions.length === 0" class="empty-state">
              <el-empty description="暂无持仓" :image-size="60" />
            </div>
            <div v-else class="position-items">
              <div
                v-for="position in positions"
                :key="position.symbol"
                class="position-item"
                :class="{ active: position.symbol === currentSymbol }"
                @click="switchSymbol(position.symbol)"
              >
                <div class="position-header">
                  <span class="symbol">{{ position.symbol }}</span>
                  <span class="name">{{ position.name }}</span>
                </div>
                <div class="position-info">
                  <div class="info-row">
                    <span class="label">持仓:</span>
                    <span class="value">{{ position.quantity }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">可用:</span>
                    <span class="value">{{ position.available }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">成本:</span>
                    <span class="value">{{ position.avgCost.toFixed(2) }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">现价:</span>
                    <span class="value">{{ position.currentPrice.toFixed(2) }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">盈亏:</span>
                    <span class="value" :class="position.profit >= 0 ? 'profit' : 'loss'">
                      {{ position.profit >= 0 ? '+' : '' }}{{ position.profit.toFixed(2) }}
                      ({{ position.profitPercent.toFixed(2) }}%)
                    </span>
                  </div>
                </div>
                <div class="position-actions">
                  <el-button type="text" size="small" @click.stop="quickSell(position)">
                    平仓
                  </el-button>
                  <el-button type="text" size="small" @click.stop="addPosition(position)">
                    加仓
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：委托和成交 -->
      <div class="workspace-right">
        <!-- 委托列表 -->
        <div class="orders-panel panel">
          <el-tabs v-model="ordersTab" type="card">
            <!-- 当日委托 -->
            <el-tab-pane label="当日委托" name="orders">
              <div class="orders-container">
                <div class="orders-header">
                  <span>时间</span>
                  <span>代码</span>
                  <span>名称</span>
                  <span>方向</span>
                  <span>价格</span>
                  <span>数量</span>
                  <span>状态</span>
                  <span>操作</span>
                </div>
                <div class="orders-list">
                  <div
                    v-for="order in todayOrders"
                    :key="order.id"
                    class="order-item"
                    :class="order.side"
                  >
                    <span class="time">{{ formatOrderTime(order.createTime) }}</span>
                    <span class="symbol">{{ order.symbol }}</span>
                    <span class="name">{{ order.name }}</span>
                    <span class="side">{{ order.side === 'buy' ? '买入' : '卖出' }}</span>
                    <span class="price">{{ order.price.toFixed(2) }}</span>
                    <span class="quantity">{{ order.quantity }}</span>
                    <span class="status" :class="`status-${order.status}`">
                      {{ getOrderStatusText(order.status) }}
                    </span>
                    <span class="actions">
                      <el-button
                        v-if="order.status === 'pending'"
                        type="text"
                        size="small"
                        @click="cancelOrder(order.id)"
                      >
                        撤单
                      </el-button>
                    </span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 当日成交 -->
            <el-tab-pane label="当日成交" name="deals">
              <div class="deals-container">
                <div class="deals-header">
                  <span>时间</span>
                  <span>代码</span>
                  <span>名称</span>
                  <span>方向</span>
                  <span>价格</span>
                  <span>数量</span>
                  <span>金额</span>
                </div>
                <div class="deals-list">
                  <div
                    v-for="deal in todayDeals"
                    :key="deal.id"
                    class="deal-item"
                    :class="deal.side"
                  >
                    <span class="time">{{ formatOrderTime(deal.dealTime) }}</span>
                    <span class="symbol">{{ deal.symbol }}</span>
                    <span class="name">{{ deal.name }}</span>
                    <span class="side">{{ deal.side === 'buy' ? '买入' : '卖出' }}</span>
                    <span class="price">{{ deal.price.toFixed(2) }}</span>
                    <span class="quantity">{{ deal.quantity }}</span>
                    <span class="amount">{{ formatAmount(deal.amount) }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 历史记录 -->
            <el-tab-pane label="历史" name="history">
              <div class="history-filter">
                <el-date-picker
                  v-model="historyDateRange"
                  type="daterange"
                  size="small"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                  @change="loadHistoryOrders"
                />
              </div>
              <div class="history-list">
                <!-- 历史记录列表 -->
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 策略监控 -->
        <div class="strategy-monitor-panel panel">
          <div class="panel-header">
            <div class="panel-title">策略监控</div>
            <el-button type="text" size="small" @click="showStrategyManager = true">
              <el-icon><Setting /></el-icon>
            </el-button>
          </div>

          <div class="strategy-list">
            <div
              v-for="strategy in activeStrategies"
              :key="strategy.id"
              class="strategy-item"
              :class="{ running: strategy.status === 'running' }"
            >
              <div class="strategy-header">
                <span class="name">{{ strategy.name }}</span>
                <el-switch
                  v-model="strategy.enabled"
                  size="small"
                  @change="toggleStrategy(strategy.id)"
                />
              </div>
              <div class="strategy-stats">
                <div class="stat">
                  <span class="label">今日盈亏:</span>
                  <span class="value" :class="strategy.todayProfit >= 0 ? 'profit' : 'loss'">
                    {{ formatAmount(strategy.todayProfit) }}
                  </span>
                </div>
                <div class="stat">
                  <span class="label">信号:</span>
                  <span class="value">{{ strategy.signalCount }}</span>
                </div>
              </div>
              <div class="strategy-progress">
                <el-progress
                  :percentage="strategy.progress"
                  :status="strategy.status === 'error' ? 'exception' : ''"
                  :stroke-width="4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="terminal-statusbar">
      <div class="statusbar-left">
        <span class="status-item">
          <el-icon><Connection /></el-icon>
          连接状态: <span :class="`status-${connectionStatus}`">{{ connectionStatusText }}</span>
        </span>
        <span class="status-item">
          <el-icon><Timer /></el-icon>
          延迟: <span class="latency">{{ latency }}ms</span>
        </span>
      </div>

      <div class="statusbar-center">
        <span class="account-info">
          <span class="label">总资产:</span>
          <span class="value">¥{{ formatAmount(accountInfo.totalAssets) }}</span>
        </span>
        <span class="account-info">
          <span class="label">可用:</span>
          <span class="value">¥{{ formatAmount(accountInfo.availableBalance) }}</span>
        </span>
        <span class="account-info">
          <span class="label">持仓:</span>
          <span class="value">¥{{ formatAmount(accountInfo.positionValue) }}</span>
        </span>
        <span class="account-info">
          <span class="label">今日盈亏:</span>
          <span class="value" :class="accountInfo.todayProfit >= 0 ? 'profit' : 'loss'">
            {{ accountInfo.todayProfit >= 0 ? '+' : '' }}{{ formatAmount(accountInfo.todayProfit) }}
          </span>
        </span>
      </div>

      <div class="statusbar-right">
        <span class="status-item">
          <el-icon><Bell /></el-icon>
          <el-badge :value="notifications.length" :max="99" />
        </span>
        <span class="status-item keyboard-hint">
          按 <kbd>?</kbd> 查看快捷键
        </span>
      </div>
    </div>

    <!-- 快捷键帮助弹窗 -->
    <el-dialog
      v-model="showShortcutHelp"
      title="键盘快捷键"
      width="600px"
    >
      <div class="shortcut-help">
        <div class="shortcut-section">
          <h4>交易操作</h4>
          <div class="shortcut-item">
            <kbd>F1</kbd> <span>快速买入</span>
          </div>
          <div class="shortcut-item">
            <kbd>F2</kbd> <span>快速卖出</span>
          </div>
          <div class="shortcut-item">
            <kbd>F3</kbd> <span>撤销所有委托</span>
          </div>
          <div class="shortcut-item">
            <kbd>F4</kbd> <span>平仓当前股票</span>
          </div>
          <div class="shortcut-item">
            <kbd>Enter</kbd> <span>确认下单</span>
          </div>
          <div class="shortcut-item">
            <kbd>Esc</kbd> <span>取消操作</span>
          </div>
        </div>

        <div class="shortcut-section">
          <h4>导航操作</h4>
          <div class="shortcut-item">
            <kbd>↑ ↓</kbd> <span>调整价格</span>
          </div>
          <div class="shortcut-item">
            <kbd>← →</kbd> <span>调整数量</span>
          </div>
          <div class="shortcut-item">
            <kbd>Tab</kbd> <span>切换输入框</span>
          </div>
          <div class="shortcut-item">
            <kbd>/</kbd> <span>搜索股票</span>
          </div>
          <div class="shortcut-item">
            <kbd>1-9</kbd> <span>快速切换自选股</span>
          </div>
        </div>

        <div class="shortcut-section">
          <h4>图表操作</h4>
          <div class="shortcut-item">
            <kbd>+</kbd> <span>放大图表</span>
          </div>
          <div class="shortcut-item">
            <kbd>-</kbd> <span>缩小图表</span>
          </div>
          <div class="shortcut-item">
            <kbd>R</kbd> <span>重置图表</span>
          </div>
          <div class="shortcut-item">
            <kbd>D</kbd> <span>切换画线工具</span>
          </div>
          <div class="shortcut-item">
            <kbd>I</kbd> <span>添加指标</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Grid, Setting, FullScreen, Close, Monitor, Aim,
  CopyDocument, ArrowDown, Refresh, Connection, Timer, Bell
} from '@element-plus/icons-vue'
import { useKeyboardShortcuts } from '@/composables/keyboard/useKeyboardShortcuts'
import { useWebSocket } from '@/composables/useWebSocket'
import { useTradingStore } from '@/stores/modules/trading'
import { useMarketStore } from '@/stores/modules/market'
import AdvancedTradingChart from '@/components/charts/AdvancedTradingChart.vue'
import { formatAmount, formatVolume, formatTradeTime, formatOrderTime } from '@/utils/formatters'

// Store
const tradingStore = useTradingStore()
const marketStore = useMarketStore()

// 状态管理
const isFullscreen = ref(false)
const currentLayout = ref('default')
const searchKeyword = ref('')
const currentSymbol = ref('000001')
const selectedPeriod = ref('1m')
const chartType = ref<'candle' | 'line'>('candle')
const selectedTool = ref('')
const activeIndicators = ref<string[]>(['MA', 'MACD', 'VOL'])
const depthTab = ref('orderbook')
const ordersTab = ref('orders')
const selectedAccount = ref('')
const orderSide = ref<'buy' | 'sell'>('buy')
const orderType = ref('limit')
const orderPrice = ref(0)
const orderQuantity = ref(100)
const advancedOptionsVisible = ref<string[]>([])
const stopLossEnabled = ref(false)
const stopLossPrice = ref(0)
const takeProfitEnabled = ref(false)
const takeProfitPrice = ref(0)
const icebergEnabled = ref(false)
const icebergQty = ref(0)
const submittingOrder = ref(false)
const showSettings = ref(false)
const showWatchlistManager = ref(false)
const showStrategyManager = ref(false)
const showShortcutHelp = ref(false)
const historyDateRange = ref<[Date, Date]>()
const notifications = ref([])
const latency = ref(0)
const connectionStatus = ref('connected')
const marketStatus = ref('trading')

// 图表相关
const chartContainer = ref<HTMLElement>()
const chartHeight = computed(() => {
  if (isFullscreen.value) return 600
  if (currentLayout.value === 'focus') return 500
  return 400
})

// 模拟数据
const currentStock = ref({
  symbol: '000001',
  name: '平安银行',
  price: 12.50,
  change: 0.15,
  changePercent: 1.21,
  open: 12.35,
  high: 12.68,
  low: 12.30,
  close: 12.50,
  volume: 128500000,
  amount: 1605625000,
  turnover: 1.32,
  amplitude: 3.08,
  bid1: 12.49,
  ask1: 12.51
})

const watchlist = ref([
  { symbol: '000001', name: '平安银行', price: 12.50, change: 0.15 },
  { symbol: '000002', name: '万科A', price: 23.80, change: -0.32 },
  { symbol: '600036', name: '招商银行', price: 35.60, change: 0.48 }
])

const orderbook = ref({
  asks: Array.from({ length: 10 }, (_, i) => ({
    price: 12.51 + i * 0.01,
    volume: Math.floor(Math.random() * 10000) * 100,
    cumulative: 0
  })).reverse(),
  bids: Array.from({ length: 10 }, (_, i) => ({
    price: 12.49 - i * 0.01,
    volume: Math.floor(Math.random() * 10000) * 100,
    cumulative: 0
  }))
})

const recentTrades = ref(
  Array.from({ length: 50 }, (_, i) => ({
    id: `trade-${i}`,
    timestamp: new Date(Date.now() - i * 1000),
    price: 12.50 + (Math.random() - 0.5) * 0.1,
    volume: Math.floor(Math.random() * 1000) * 100,
    side: Math.random() > 0.5 ? 'buy' : 'sell'
  }))
)

const capitalFlow = ref({
  inflow: *********,
  outflow: *********,
  net: *********
})

const positions = ref([
  {
    symbol: '000001',
    name: '平安银行',
    quantity: 1000,
    available: 1000,
    avgCost: 12.20,
    currentPrice: 12.50,
    profit: 300,
    profitPercent: 2.46
  }
])

const accounts = ref([
  { id: 'main', name: '主账户', balance: 100000 },
  { id: 'margin', name: '融资账户', balance: 50000 }
])

const todayOrders = ref([])
const todayDeals = ref([])
const activeStrategies = ref([])

const accountInfo = ref({
  totalAssets: 150000,
  availableBalance: 50000,
  positionValue: 100000,
  todayProfit: 1250
})

// 计算属性
const currentTime = computed(() => {
  const now = new Date()
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
})

const marketStatusText = computed(() => {
  const statusMap = {
    'pre': '盘前',
    'trading': '交易中',
    'break': '午休',
    'after': '盘后',
    'closed': '已收盘'
  }
  return statusMap[marketStatus.value] || '未知'
})

const connectionStatusText = computed(() => {
  const statusMap = {
    'connected': '已连接',
    'connecting': '连接中',
    'disconnected': '已断开',
    'error': '连接错误'
  }
  return statusMap[connectionStatus.value] || '未知'
})

const priceDirection = computed(() => {
  return currentStock.value.change >= 0 ? 'up' : 'down'
})

const orderAmount = computed(() => {
  return orderPrice.value * orderQuantity.value
})

const availableBalance = computed(() => {
  return accountInfo.value.availableBalance
})

const availableShares = computed(() => {
  const position = positions.value.find(p => p.symbol === currentSymbol.value)
  return position?.available || 0
})

const maxOrderQuantity = computed(() => {
  if (orderSide.value === 'buy') {
    return Math.floor(availableBalance.value / orderPrice.value / 100) * 100
  } else {
    return availableShares.value
  }
})

// 周期选项
const periods = [
  { label: '1分', value: '1m' },
  { label: '5分', value: '5m' },
  { label: '15分', value: '15m' },
  { label: '30分', value: '30m' },
  { label: '60分', value: '60m' },
  { label: '日线', value: '1d' },
  { label: '周线', value: '1w' },
  { label: '月线', value: '1M' }
]

// 可用指标
const availableIndicators = [
  { id: 'MA', name: '移动平均线' },
  { id: 'EMA', name: '指数移动平均线' },
  { id: 'MACD', name: 'MACD' },
  { id: 'KDJ', name: 'KDJ' },
  { id: 'RSI', name: 'RSI' },
  { id: 'BOLL', name: '布林带' },
  { id: 'VOL', name: '成交量' },
  { id: 'SAR', name: 'SAR' },
  { id: 'DMI', name: 'DMI' },
  { id: 'OBV', name: 'OBV' }
]

// 画图工具
const drawingTools = [
  { id: 'line', name: '趋势线', icon: 'Edit' },
  { id: 'hline', name: '水平线', icon: 'Minus' },
  { id: 'vline', name: '垂直线', icon: 'MoreFilled' },
  { id: 'rect', name: '矩形', icon: 'Files' },
  { id: 'text', name: '文字', icon: 'EditPen' },
  { id: 'fibonacci', name: '斐波那契', icon: 'TrendCharts' }
]

// WebSocket连接
const { send, subscribe, unsubscribe } = useWebSocket('/ws/trading')

// 方法
const searchStocks = async (queryString: string, cb: Function) => {
  const results = await marketStore.searchStocks(queryString)
  cb(results)
}

const handleStockSelect = (item: any) => {
  switchSymbol(item.symbol)
}

const quickSearch = () => {
  // 快速搜索逻辑
}

const switchSymbol = (symbol: string) => {
  currentSymbol.value = symbol
  loadStockData(symbol)
  subscribeMarketData(symbol)
}

const removeFromWatchlist = (symbol: string) => {
  const index = watchlist.value.findIndex(s => s.symbol === symbol)
  if (index > -1) {
    watchlist.value.splice(index, 1)
    ElMessage.success('已从自选股移除')
  }
}

const handleLayoutChange = (layout: string) => {
  currentLayout.value = layout
  nextTick(() => {
    // 触发图表重绘
    window.dispatchEvent(new Event('resize'))
  })
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const changePeriod = (period: string) => {
  selectedPeriod.value = period
  loadChartData(currentSymbol.value, period)
}

const toggleIndicator = (indicatorId: string) => {
  const index = activeIndicators.value.indexOf(indicatorId)
  if (index > -1) {
    activeIndicators.value.splice(index, 1)
  } else {
    activeIndicators.value.push(indicatorId)
  }
}

const selectDrawingTool = (toolId: string) => {
  selectedTool.value = selectedTool.value === toolId ? '' : toolId
}

const handlePriceClick = (price: number) => {
  orderPrice.value = price
}

const handleDrawingComplete = (drawing: any) => {
  // 保存画线数据
  console.log('Drawing completed:', drawing)
}

const setOrderPrice = (price: number) => {
  orderPrice.value = price
}

const adjustPrice = (step: number) => {
  orderPrice.value = Math.max(0, orderPrice.value + step)
}

const adjustQuantity = (step: number) => {
  orderQuantity.value = Math.max(100, Math.min(maxOrderQuantity.value, orderQuantity.value + step))
}

const setQuantityRatio = (ratio: number) => {
  orderQuantity.value = Math.floor(maxOrderQuantity.value * ratio / 100) * 100
}

const handlePriceFocus = () => {
  // 价格输入框获得焦点时的处理
}

const submitOrder = async () => {
  if (!orderPrice.value || !orderQuantity.value) {
    ElMessage.warning('请输入价格和数量')
    return
  }

  submittingOrder.value = true
  try {
    const order = {
      symbol: currentSymbol.value,
      side: orderSide.value,
      type: orderType.value,
      price: orderPrice.value,
      quantity: orderQuantity.value,
      stopLoss: stopLossEnabled.value ? stopLossPrice.value : null,
      takeProfit: takeProfitEnabled.value ? takeProfitPrice.value : null,
      iceberg: icebergEnabled.value ? icebergQty.value : null
    }

    await tradingStore.submitOrder(order)
    ElMessage.success('订单提交成功')
    
    // 重置表单
    orderQuantity.value = 100
    stopLossEnabled.value = false
    takeProfitEnabled.value = false
    icebergEnabled.value = false
  } catch (error) {
    ElMessage.error('订单提交失败')
  } finally {
    submittingOrder.value = false
  }
}

const cancelOrder = async (orderId: string) => {
  try {
    await tradingStore.cancelOrder(orderId)
    ElMessage.success('撤单成功')
  } catch (error) {
    ElMessage.error('撤单失败')
  }
}

const quickSell = (position: any) => {
  orderSide.value = 'sell'
  orderPrice.value = currentStock.value.bid1
  orderQuantity.value = position.available
  ElMessage.info('已设置平仓参数，请确认后下单')
}

const addPosition = (position: any) => {
  orderSide.value = 'buy'
  orderPrice.value = currentStock.value.ask1
  orderQuantity.value = 100
  ElMessage.info('已设置加仓参数，请确认后下单')
}

const refreshPositions = async () => {
  await tradingStore.refreshPositions()
}

const loadHistoryOrders = () => {
  // 加载历史订单
}

const toggleStrategy = (strategyId: string) => {
  // 切换策略状态
}

const getOrderStatusText = (status: string) => {
  const statusMap = {
    'pending': '未成交',
    'partial': '部分成交',
    'filled': '已成交',
    'cancelled': '已撤单',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

const getVolumeBarWidth = (volume: number, type: 'ask' | 'bid') => {
  const maxVolume = Math.max(
    ...orderbook.value.asks.map(a => a.volume),
    ...orderbook.value.bids.map(b => b.volume)
  )
  return `${(volume / maxVolume) * 100}%`
}

const loadStockData = async (symbol: string) => {
  // 加载股票数据
  const data = await marketStore.getStockInfo(symbol)
  if (data) {
    currentStock.value = data
  }
}

const loadChartData = async (symbol: string, period: string) => {
  // 加载图表数据
}

const subscribeMarketData = (symbol: string) => {
  // 订阅实时数据
  subscribe(`market.${symbol}`, (data: any) => {
    // 更新实时数据
    currentStock.value = { ...currentStock.value, ...data }
  })

  subscribe(`orderbook.${symbol}`, (data: any) => {
    orderbook.value = data
  })

  subscribe(`trades.${symbol}`, (data: any) => {
    recentTrades.value.unshift(data)
    if (recentTrades.value.length > 50) {
      recentTrades.value.pop()
    }
  })
}

// 键盘快捷键
const { register, unregister } = useKeyboardShortcuts()

const setupShortcuts = () => {
  register('F1', () => {
    orderSide.value = 'buy'
    ElMessage.info('切换到买入模式')
  })

  register('F2', () => {
    orderSide.value = 'sell'
    ElMessage.info('切换到卖出模式')
  })

  register('F3', async () => {
    await ElMessageBox.confirm('确认撤销所有委托？', '提示')
    await tradingStore.cancelAllOrders()
    ElMessage.success('已撤销所有委托')
  })

  register('F4', () => {
    const position = positions.value.find(p => p.symbol === currentSymbol.value)
    if (position) {
      quickSell(position)
    }
  })

  register('Enter', () => {
    if (!submittingOrder.value) {
      submitOrder()
    }
  })

  register('Escape', () => {
    // 取消当前操作
  })

  register('?', () => {
    showShortcutHelp.value = true
  })

  register('/', () => {
    // 聚焦搜索框
    const searchInput = document.querySelector('.symbol-search input') as HTMLInputElement
    searchInput?.focus()
  })

  // 数字键快速切换自选股
  for (let i = 1; i <= 9; i++) {
    register(String(i), () => {
      if (watchlist.value[i - 1]) {
        switchSymbol(watchlist.value[i - 1].symbol)
      }
    })
  }
}

// 生命周期
onMounted(() => {
  setupShortcuts()
  loadStockData(currentSymbol.value)
  subscribeMarketData(currentSymbol.value)
  
  // 初始化账户
  if (accounts.value.length > 0) {
    selectedAccount.value = accounts.value[0].id
  }

  // 启动实时数据更新
  setInterval(() => {
    // 模拟延迟更新
    latency.value = Math.floor(Math.random() * 50) + 10
  }, 1000)
})

onUnmounted(() => {
  unregister()
  unsubscribe(`market.${currentSymbol.value}`)
  unsubscribe(`orderbook.${currentSymbol.value}`)
  unsubscribe(`trades.${currentSymbol.value}`)
})

// 监听
watch(currentSymbol, (newSymbol) => {
  loadStockData(newSymbol)
})
</script>

<style lang="scss" scoped>
.professional-trading-terminal {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0a0e17;
  color: #e4e4e7;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;

  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }
}

// 顶部工具栏
.terminal-toolbar {
  height: 48px;
  background: linear-gradient(180deg, #1a1f2e 0%, #151922 100%);
  border-bottom: 1px solid #2a2d3a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .symbol-search {
      :deep(.el-autocomplete) {
        .el-input__wrapper {
          background: #0f1419;
          border: 1px solid #2a2d3a;
          box-shadow: none;

          &:hover {
            border-color: #3b82f6;
          }
        }

        .el-input__inner {
          color: #e4e4e7;
        }
      }
    }

    .quick-symbols {
      display: flex;
      align-items: center;
      gap: 8px;

      .symbol-tag {
        background: #1a1f2e;
        border: 1px solid #2a2d3a;
        color: #e4e4e7;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #3b82f6;
          transform: translateY(-1px);
        }

        &.el-tag--primary {
          background: #3b82f6;
          border-color: #3b82f6;
        }

        .tag-symbol {
          margin-right: 4px;
          font-weight: 500;
        }

        .tag-price {
          font-size: 12px;

          &.up {
            color: #10b981;
          }

          &.down {
            color: #ef4444;
          }
        }
      }
    }
  }

  .toolbar-center {
    .market-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 6px;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        &.status-trading .status-dot {
          background: #10b981;
        }

        &.status-pre .status-dot,
        &.status-after .status-dot {
          background: #f59e0b;
        }

        &.status-closed .status-dot {
          background: #6b7280;
        }

        .status-text {
          font-size: 12px;
          color: #9ca3af;
        }
      }

      .market-time {
        font-size: 14px;
        font-weight: 500;
        color: #e4e4e7;
      }
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-button {
      color: #9ca3af;

      &:hover {
        color: #3b82f6;
      }
    }
  }
}

// 主工作区
.terminal-workspace {
  flex: 1;
  display: flex;
  gap: 1px;
  background: #2a2d3a;
  overflow: hidden;

  &.layout-default {
    .workspace-left {
      flex: 3;
    }
    .workspace-center {
      flex: 1.5;
    }
    .workspace-right {
      flex: 2;
    }
  }

  &.layout-focus {
    .workspace-left {
      flex: 4;
    }
    .workspace-center {
      flex: 1;
    }
    .workspace-right {
      flex: 1.5;
    }
  }

  &.layout-multi {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  .workspace-left,
  .workspace-center,
  .workspace-right {
    background: #0a0e17;
    display: flex;
    flex-direction: column;
    gap: 1px;
  }
}

// 面板通用样式
.panel {
  background: #0f1419;
  border: 1px solid #1a1f2e;
  display: flex;
  flex-direction: column;

  .panel-header {
    height: 40px;
    background: linear-gradient(180deg, #1a1f2e 0%, #151922 100%);
    border-bottom: 1px solid #2a2d3a;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;

    .panel-title {
      font-size: 14px;
      font-weight: 500;
      color: #e4e4e7;

      .symbol-info {
        display: flex;
        align-items: center;
        gap: 8px;

        strong {
          font-size: 16px;
        }

        .name {
          color: #9ca3af;
        }

        .price {
          font-weight: 600;

          &.up {
            color: #10b981;
          }

          &.down {
            color: #ef4444;
          }
        }

        .change {
          font-size: 14px;

          &.up {
            color: #10b981;
          }

          &.down {
            color: #ef4444;
          }
        }
      }
    }

    .panel-tools {
      display: flex;
      align-items: center;
      gap: 8px;

      :deep(.el-button-group) {
        .el-button {
          background: #0a0e17;
          border-color: #2a2d3a;
          color: #9ca3af;

          &:hover {
            background: #1a1f2e;
            border-color: #3b82f6;
            color: #3b82f6;
          }

          &.el-button--primary {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
          }
        }
      }
    }
  }
}

// 图表面板
.chart-panel {
  flex: 1;

  .chart-container {
    flex: 1;
    background: #0a0e17;
    position: relative;
  }

  .chart-info-bar {
    height: 32px;
    background: #0f1419;
    border-top: 1px solid #1a1f2e;
    display: flex;
    align-items: center;
    padding: 0 12px;
    gap: 24px;

    .info-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;

      .label {
        color: #6b7280;
      }

      .value {
        color: #e4e4e7;
        font-weight: 500;
      }
    }
  }
}

// 深度面板
.market-depth-panel {
  height: 350px;

  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__header {
      margin: 0;
      background: #0f1419;
      border-bottom: 1px solid #1a1f2e;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        color: #9ca3af;
        border: none;
        height: 36px;
        line-height: 36px;

        &:hover {
          color: #3b82f6;
        }

        &.is-active {
          color: #3b82f6;
          background: #1a1f2e;
        }
      }
    }

    .el-tabs__content {
      flex: 1;
      padding: 0;
    }
  }

  .orderbook-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-size: 12px;

    .orderbook-header {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px;
      color: #6b7280;
      border-bottom: 1px solid #1a1f2e;
    }

    .asks-list,
    .bids-list {
      flex: 1;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #2a2d3a;
        border-radius: 2px;
      }
    }

    .order-row {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      cursor: pointer;
      position: relative;
      transition: background 0.2s;

      &:hover {
        background: rgba(59, 130, 246, 0.1);
      }

      .price {
        font-weight: 500;
      }

      .volume {
        color: #9ca3af;
      }

      .cumulative {
        color: #6b7280;
      }

      .volume-bar {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        opacity: 0.2;
      }

      &.ask {
        .price {
          color: #ef4444;
        }

        .volume-bar {
          background: #ef4444;
        }
      }

      &.bid {
        .price {
          color: #10b981;
        }

        .volume-bar {
          background: #10b981;
        }
      }
    }

    .current-price-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 8px;
      background: #1a1f2e;
      border-top: 1px solid #2a2d3a;
      border-bottom: 1px solid #2a2d3a;

      .current-price {
        font-size: 16px;
        font-weight: 600;

        &.up {
          color: #10b981;
        }

        &.down {
          color: #ef4444;
        }
      }

      .price-change {
        font-size: 12px;

        &.up {
          color: #10b981;
        }

        &.down {
          color: #ef4444;
        }
      }
    }
  }

  .trades-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-size: 12px;

    .trades-header {
      display: flex;
      justify-content: space-between;
      padding: 8px 12px;
      color: #6b7280;
      border-bottom: 1px solid #1a1f2e;
    }

    .trades-list {
      flex: 1;
      overflow-y: auto;

      .trade-row {
        display: flex;
        justify-content: space-between;
        padding: 4px 12px;
        border-bottom: 1px solid #0a0e17;

        .time {
          color: #6b7280;
        }

        .price {
          font-weight: 500;
        }

        .volume {
          color: #9ca3af;
        }

        .side-indicator {
          width: 20px;
          text-align: center;
          font-weight: 600;
        }

        &.buy {
          .price,
          .side-indicator {
            color: #10b981;
          }
        }

        &.sell {
          .price,
          .side-indicator {
            color: #ef4444;
          }
        }
      }
    }
  }

  .capital-flow {
    padding: 12px;

    .flow-summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;

      .flow-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .label {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .value {
          font-size: 14px;
          font-weight: 600;
        }

        &.inflow .value {
          color: #10b981;
        }

        &.outflow .value {
          color: #ef4444;
        }

        &.net .value {
          &.positive {
            color: #10b981;
          }

          &.negative {
            color: #ef4444;
          }
        }
      }
    }

    .flow-chart {
      height: 200px;
      background: #0a0e17;
      border-radius: 4px;
    }
  }
}

// 下单面板
.order-panel {
  .order-side-tabs {
    display: flex;
    height: 40px;
    background: #0a0e17;

    .side-tab {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.2s;
      border-bottom: 2px solid transparent;

      .tab-label {
        font-size: 14px;
        font-weight: 500;
      }

      .tab-shortcut {
        font-size: 11px;
        color: #6b7280;
        background: #1a1f2e;
        padding: 2px 6px;
        border-radius: 4px;
      }

      &.buy {
        &.active,
        &:hover {
          background: rgba(16, 185, 129, 0.1);
          border-bottom-color: #10b981;

          .tab-label {
            color: #10b981;
          }
        }
      }

      &.sell {
        &.active,
        &:hover {
          background: rgba(239, 68, 68, 0.1);
          border-bottom-color: #ef4444;

          .tab-label {
            color: #ef4444;
          }
        }
      }
    }
  }

  .order-form {
    padding: 16px;

    .form-row {
      margin-bottom: 16px;

      :deep(.el-radio-group) {
        width: 100%;

        .el-radio-button__wrapper {
          background: #0a0e17;
          border-color: #2a2d3a;
          color: #9ca3af;

          &:hover {
            color: #3b82f6;
          }
        }

        .el-radio-button__original:checked + .el-radio-button__inner {
          background: #3b82f6;
          border-color: #3b82f6;
          color: white;
        }
      }

      .input-group {
        .input-label {
          display: block;
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        :deep(.el-input-number) {
          width: 100%;

          .el-input__wrapper {
            background: #0a0e17;
            border-color: #2a2d3a;
          }

          .el-input__inner {
            color: #e4e4e7;
            font-weight: 500;
          }
        }

        .quick-price-buttons,
        .quick-quantity-buttons {
          display: flex;
          gap: 4px;
          margin-top: 8px;

          .el-button {
            flex: 1;
            background: #0a0e17;
            border-color: #2a2d3a;
            color: #9ca3af;
            font-size: 12px;

            &:hover {
              background: #1a1f2e;
              border-color: #3b82f6;
              color: #3b82f6;
            }
          }
        }
      }

      .amount-display,
      .available-display {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        background: #0a0e17;
        border-radius: 4px;
        margin-bottom: 8px;

        .label {
          font-size: 12px;
          color: #6b7280;
        }

        .value {
          font-size: 14px;
          font-weight: 600;
          color: #e4e4e7;
        }
      }
    }

    :deep(.el-collapse) {
      border: none;

      .el-collapse-item__header {
        background: #0a0e17;
        border: 1px solid #2a2d3a;
        border-radius: 4px;
        color: #9ca3af;
        height: 36px;

        .advanced-title {
          font-size: 12px;
        }
      }

      .el-collapse-item__content {
        background: #0a0e17;
        padding: 12px;
      }
    }

    .advanced-options {
      .option-row {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        :deep(.el-checkbox) {
          .el-checkbox__label {
            color: #9ca3af;
            font-size: 12px;
          }
        }

        :deep(.el-input-number) {
          .el-input__wrapper {
            background: #0f1419;
            border-color: #2a2d3a;
          }

          .el-input__inner {
            color: #e4e4e7;
          }
        }
      }
    }

    .form-actions {
      margin-top: 16px;

      .el-button {
        height: 40px;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .shortcut-hints {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 12px;

      .hint {
        font-size: 11px;
        color: #6b7280;

        kbd {
          background: #1a1f2e;
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid #2a2d3a;
          margin-right: 4px;
        }
      }
    }
  }
}

// 持仓面板
.position-panel {
  flex: 1;

  .position-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }

    .position-items {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .position-item {
        background: #0a0e17;
        border: 1px solid #1a1f2e;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #2a2d3a;
          transform: translateY(-1px);
        }

        &.active {
          border-color: #3b82f6;
          background: rgba(59, 130, 246, 0.05);
        }

        .position-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .symbol {
            font-size: 14px;
            font-weight: 600;
            color: #e4e4e7;
          }

          .name {
            font-size: 12px;
            color: #6b7280;
          }
        }

        .position-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 6px;
          font-size: 12px;

          .info-row {
            display: flex;
            justify-content: space-between;

            .label {
              color: #6b7280;
            }

            .value {
              color: #e4e4e7;
              font-weight: 500;

              &.profit {
                color: #10b981;
              }

              &.loss {
                color: #ef4444;
              }
            }
          }
        }

        .position-actions {
          display: flex;
          gap: 8px;
          margin-top: 8px;

          .el-button {
            flex: 1;
            background: #0f1419;
            border: 1px solid #2a2d3a;
            color: #9ca3af;

            &:hover {
              border-color: #3b82f6;
              color: #3b82f6;
            }
          }
        }
      }
    }
  }
}

// 委托和成交面板
.orders-panel {
  flex: 1;

  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__content {
      flex: 1;
      overflow: hidden;
    }
  }

  .orders-container,
  .deals-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .orders-header,
    .deals-header {
      display: grid;
      grid-template-columns: 60px 60px 80px 50px 60px 60px 80px 60px;
      padding: 8px 12px;
      font-size: 12px;
      color: #6b7280;
      border-bottom: 1px solid #1a1f2e;
    }

    .orders-list,
    .deals-list {
      flex: 1;
      overflow-y: auto;

      .order-item,
      .deal-item {
        display: grid;
        grid-template-columns: 60px 60px 80px 50px 60px 60px 80px 60px;
        padding: 6px 12px;
        font-size: 12px;
        border-bottom: 1px solid #0a0e17;
        align-items: center;

        &:hover {
          background: rgba(59, 130, 246, 0.05);
        }

        .time {
          color: #6b7280;
        }

        .symbol {
          font-weight: 500;
          color: #e4e4e7;
        }

        .name {
          color: #9ca3af;
        }

        .side {
          font-weight: 500;
        }

        &.buy .side {
          color: #10b981;
        }

        &.sell .side {
          color: #ef4444;
        }

        .price {
          color: #e4e4e7;
        }

        .quantity {
          color: #9ca3af;
        }

        .status {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 4px;

          &.status-pending {
            background: rgba(251, 191, 36, 0.1);
            color: #fbbf24;
          }

          &.status-filled {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
          }

          &.status-cancelled {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
          }
        }

        .actions {
          .el-button {
            padding: 2px 8px;
            font-size: 11px;
          }
        }
      }
    }
  }

  .history-filter {
    padding: 12px;
    border-bottom: 1px solid #1a1f2e;
  }
}

// 策略监控面板
.strategy-monitor-panel {
  height: 300px;

  .strategy-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .strategy-item {
      background: #0a0e17;
      border: 1px solid #1a1f2e;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 8px;

      &.running {
        border-color: #10b981;
      }

      .strategy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .name {
          font-size: 14px;
          font-weight: 500;
          color: #e4e4e7;
        }
      }

      .strategy-stats {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;

        .stat {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;

          .label {
            color: #6b7280;
          }

          .value {
            color: #e4e4e7;
            font-weight: 500;

            &.profit {
              color: #10b981;
            }

            &.loss {
              color: #ef4444;
            }
          }
        }
      }

      :deep(.el-progress) {
        .el-progress__text {
          color: #9ca3af;
          font-size: 11px;
        }
      }
    }
  }
}

// 底部状态栏
.terminal-statusbar {
  height: 32px;
  background: linear-gradient(180deg, #151922 0%, #1a1f2e 100%);
  border-top: 1px solid #2a2d3a;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;

  .statusbar-left,
  .statusbar-center,
  .statusbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #9ca3af;

    .el-icon {
      font-size: 14px;
    }

    .status-connected {
      color: #10b981;
    }

    .status-disconnected {
      color: #ef4444;
    }

    .latency {
      color: #e4e4e7;
      font-weight: 500;
    }

    kbd {
      background: #0a0e17;
      border: 1px solid #2a2d3a;
      border-radius: 4px;
      padding: 2px 6px;
      margin: 0 4px;
      color: #e4e4e7;
      font-family: monospace;
    }
  }

  .account-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .label {
      color: #6b7280;
    }

    .value {
      color: #e4e4e7;
      font-weight: 500;

      &.profit {
        color: #10b981;
      }

      &.loss {
        color: #ef4444;
      }
    }
  }

  :deep(.el-badge) {
    sup {
      background: #ef4444;
      border: none;
    }
  }
}

// 股票建议项
.stock-suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;

  .stock-main {
    display: flex;
    align-items: center;
    gap: 8px;

    .stock-code {
      font-weight: 600;
      color: #e4e4e7;
    }

    .stock-name {
      color: #9ca3af;
      font-size: 12px;
    }
  }

  .stock-price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .price {
      font-weight: 500;
      color: #e4e4e7;
    }

    .change {
      font-size: 12px;

      &.up {
        color: #10b981;
      }

      &.down {
        color: #ef4444;
      }
    }
  }
}

// 快捷键帮助弹窗
.shortcut-help {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  .shortcut-section {
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }

    .shortcut-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      kbd {
        background: #f4f4f5;
        border: 1px solid #d4d4d8;
        border-radius: 4px;
        padding: 4px 8px;
        font-family: monospace;
        font-size: 12px;
        min-width: 40px;
        text-align: center;
      }

      span {
        color: #666;
        font-size: 13px;
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式
@media (max-width: 1440px) {
  .terminal-workspace {
    &.layout-default {
      .workspace-center {
        flex: 1.2;
      }
    }
  }
}

@media (max-width: 1280px) {
  .terminal-workspace {
    &.layout-default {
      .workspace-left {
        flex: 2.5;
      }
      .workspace-center {
        flex: 1;
      }
      .workspace-right {
        flex: 1.5;
      }
    }
  }

  .orders-container .orders-header,
  .orders-container .order-item {
    font-size: 11px;
  }
}
</style>