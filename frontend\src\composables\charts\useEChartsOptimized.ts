import { ref, shallowRef, onMounted, onUnmounted, watch, nextTick, Ref } from 'vue'
import * as echarts from 'echarts'
import { debounce } from 'lodash-es'

export interface UseEChartsOptions {
  theme?: string | object
  initOptions?: echarts.EChartsInitOpts
  loadingOptions?: object
  autoResize?: boolean
  resizeDebounce?: number
}

export function useEChartsOptimized(
  elRef: Ref<HTMLElement | undefined>,
  options: UseEChartsOptions = {}
) {
  const {
    theme = 'light',
    initOptions = {},
    loadingOptions = {
      text: '加载中...',
      color: '#409eff',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    },
    autoResize = true,
    resizeDebounce = 200
  } = options

  // 使用 shallowRef 避免深度响应式带来的性能问题
  const chartInstance = shallowRef<echarts.ECharts | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  // 图表配置缓存，避免重复渲染
  let cachedOption: echarts.EChartsOption | null = null
  
  // ResizeObserver 实例
  let resizeObserver: ResizeObserver | null = null

  // 初始化图表
  const initChart = async (): Promise<void> => {
    if (!elRef.value) {
      console.warn('Chart container element not found')
      return
    }

    try {
      // 销毁已存在的实例
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      // 创建新实例
      chartInstance.value = echarts.init(
        elRef.value,
        theme,
        {
          renderer: 'canvas',
          useDirtyRect: true, // 启用脏矩形渲染优化
          ...initOptions
        }
      )

      // 如果有缓存的配置，立即应用
      if (cachedOption) {
        chartInstance.value.setOption(cachedOption)
      }

      // 设置 ResizeObserver 监听容器大小变化
      if (autoResize && window.ResizeObserver) {
        resizeObserver = new ResizeObserver(
          debounce(() => {
            chartInstance.value?.resize({
              animation: {
                duration: 300,
                easing: 'cubicOut'
              }
            })
          }, resizeDebounce)
        )
        resizeObserver.observe(elRef.value)
      }

      error.value = null
    } catch (e) {
      error.value = e as Error
      console.error('Failed to initialize chart:', e)
    }
  }

  // 设置图表配置（优化版）
  const setOption = (
    option: echarts.EChartsOption,
    opts?: echarts.EChartsOptionConfig
  ): void => {
    if (!chartInstance.value) {
      // 缓存配置，等待初始化完成后应用
      cachedOption = option
      return
    }

    try {
      // 默认开启合并模式，避免数据闪烁
      chartInstance.value.setOption(option, {
        notMerge: false,
        lazyUpdate: true,
        ...opts
      })
      cachedOption = option
    } catch (e) {
      error.value = e as Error
      console.error('Failed to set chart option:', e)
    }
  }

  // 更新图表数据（增量更新，性能更好）
  const updateData = (
    seriesData: any[],
    seriesIndex: number = 0
  ): void => {
    if (!chartInstance.value) return

    try {
      chartInstance.value.setOption({
        series: [{
          id: seriesIndex,
          data: seriesData
        }]
      })
    } catch (e) {
      error.value = e as Error
      console.error('Failed to update chart data:', e)
    }
  }

  // 显示加载动画
  const showLoading = (): void => {
    loading.value = true
    chartInstance.value?.showLoading('default', loadingOptions)
  }

  // 隐藏加载动画
  const hideLoading = (): void => {
    loading.value = false
    chartInstance.value?.hideLoading()
  }

  // 手动调整大小
  const resize = (): void => {
    chartInstance.value?.resize({
      animation: {
        duration: 300,
        easing: 'cubicOut'
      }
    })
  }

  // 获取图表实例
  const getInstance = (): echarts.ECharts | null => {
    return chartInstance.value
  }

  // 销毁图表
  const dispose = (): void => {
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
    
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }
    
    cachedOption = null
    error.value = null
  }

  // 导出图表为图片
  const exportImage = (type: 'png' | 'jpg' = 'png'): string | undefined => {
    return chartInstance.value?.getDataURL({
      type,
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
  }

  // 监听元素变化，自动初始化
  watch(
    () => elRef.value,
    async (el) => {
      if (el) {
        await nextTick()
        await initChart()
      }
    },
    { immediate: true }
  )

  // 组件卸载时清理
  onUnmounted(() => {
    dispose()
  })

  return {
    chartInstance: chartInstance as Ref<echarts.ECharts | null>,
    loading,
    error,
    initChart,
    setOption,
    updateData,
    showLoading,
    hideLoading,
    resize,
    getInstance,
    dispose,
    exportImage
  }
}

// 导出类型
export type { EChartsOption, EChartsType } from 'echarts'