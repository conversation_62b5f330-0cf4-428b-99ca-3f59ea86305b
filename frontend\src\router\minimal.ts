import { createRouter, createWebHistory } from 'vue-router'
import { h } from 'vue'

// 创建一个最小化的路由器，用于测试基本功能
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: {
        render() {
          return h('div', {
            style: 'padding: 2rem; text-align: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);'
          }, [
            h('div', {
              style: 'background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto;'
            }, [
              h('h1', { style: 'color: #2c3e50; margin-bottom: 1rem;' }, '🚀 量化投资平台'),
              h('p', { style: 'color: #6b7280; margin-bottom: 2rem;' }, 'Vue应用已成功加载！'),
              
              h('div', {
                style: 'display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;'
              }, [
                h('div', {
                  style: 'padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;'
                }, [
                  h('h3', { style: 'color: #2c3e50; margin-bottom: 0.5rem;' }, '📊 仪表盘'),
                  h('p', { style: 'color: #6b7280; font-size: 0.9rem;' }, '投资数据概览')
                ]),
                h('div', {
                  style: 'padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;'
                }, [
                  h('h3', { style: 'color: #2c3e50; margin-bottom: 0.5rem;' }, '📈 市场行情'),
                  h('p', { style: 'color: #6b7280; font-size: 0.9rem;' }, '实时市场数据')
                ]),
                h('div', {
                  style: 'padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;'
                }, [
                  h('h3', { style: 'color: #2c3e50; margin-bottom: 0.5rem;' }, '💰 智能交易'),
                  h('p', { style: 'color: #6b7280; font-size: 0.9rem;' }, '自动化交易系统')
                ]),
                h('div', {
                  style: 'padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;'
                }, [
                  h('h3', { style: 'color: #2c3e50; margin-bottom: 0.5rem;' }, '🧠 策略研发'),
                  h('p', { style: 'color: #6b7280; font-size: 0.9rem;' }, '量化策略开发')
                ])
              ]),

              h('div', {
                style: 'background: #e8f5e8; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981;'
              }, [
                h('p', { style: 'color: #065f46; font-weight: 500;' }, '✅ Vue Router 工作正常！'),
                h('p', { style: 'color: #047857; font-size: 0.9rem;' }, '应用已成功加载，可以继续配置完整的路由系统')
              ])
            ])
          ])
        }
      },
      meta: {
        title: '量化投资平台首页',
        requiresAuth: false
      }
    }
  ]
})

export default router