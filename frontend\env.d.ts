/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_WS_BASE_URL: string
  readonly VITE_ENABLE_MOCK: string
  readonly VITE_ENABLE_PWA: string
  readonly VITE_BUILD_ANALYZE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
  readonly hot?: ViteHotContext
}

interface ViteHotContext {
  readonly data: any
  accept(): void
  accept(cb: (mod: any) => void): void
  accept(dep: string, cb: (mod: any) => void): void
  accept(deps: readonly string[], cb: (mods: any[]) => void): void
  dispose(cb: (data: any) => void): void
  decline(): void
  invalidate(): void
  on<T extends string>(event: T, cb: (payload: any) => void): void
  send<T extends string>(event: T, data?: any): void
}
